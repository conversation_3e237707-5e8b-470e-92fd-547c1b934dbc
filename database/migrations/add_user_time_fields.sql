-- 添加用户最后登录时间和最近使用时间字段
-- 由于D1不支持ALTER TABLE ADD COLUMN,我们需要重建表

-- 1. 创建新的users表
CREATE TABLE IF NOT EXISTS users_new (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    email TEXT,
    password_hash TEXT NOT NULL,
    role_id TEXT NOT NULL,
    last_login_at TEXT,
    last_active_at TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- 2. 复制数据从旧表到新表
INSERT INTO users_new (id, username, email, password_hash, role_id, created_at, updated_at)
SELECT id, username, email, password_hash, role_id, created_at, updated_at
FROM users;

-- 3. 删除旧表
DROP TABLE users;

-- 4. 重命名新表
ALTER TABLE users_new RENAME TO users;

-- 5. 重建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);

