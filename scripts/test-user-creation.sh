#!/bin/bash

# 测试用户创建功能，验证新用户是否有有效的id

echo "=== 测试用户创建功能 ==="
echo ""

# 获取认证token
echo "请输入您的认证token（从浏览器localStorage中获取auth_token）："
read -r AUTH_TOKEN

if [ -z "$AUTH_TOKEN" ]; then
    echo "错误：未提供认证token"
    exit 1
fi

# 生成随机用户名
RANDOM_USER="test_user_$(date +%s)"
RANDOM_EMAIL="${RANDOM_USER}@test.com"

echo ""
echo "1. 创建测试用户..."
echo "   用户名: $RANDOM_USER"
echo "   邮箱: $RANDOM_EMAIL"

# 调用API创建用户
RESPONSE=$(curl -s -X POST \
  https://api-admin.liubao.site/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -d "{
    \"username\": \"$RANDOM_USER\",
    \"email\": \"$RANDOM_EMAIL\",
    \"password\": \"Test123456\",
    \"role_id\": \"user-role-id\"
  }")

echo ""
echo "API响应:"
echo "$RESPONSE" | jq '.'

# 提取用户ID
USER_ID=$(echo "$RESPONSE" | jq -r '.id')

if [ "$USER_ID" = "null" ] || [ -z "$USER_ID" ]; then
    echo ""
    echo "❌ 测试失败：创建的用户ID为NULL或空"
    exit 1
fi

echo ""
echo "2. 验证用户ID..."
echo "   用户ID: $USER_ID"

# 从数据库查询验证
echo ""
echo "3. 从数据库查询验证..."
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "SELECT id, username, email FROM users WHERE username = '$RANDOM_USER'"

echo ""
echo "4. 清理测试数据..."
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "DELETE FROM users WHERE username = '$RANDOM_USER'"

echo ""
echo "✅ 测试完成！新创建的用户有有效的UUID"

