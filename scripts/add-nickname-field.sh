#!/bin/bash

# 为users表添加nickname字段的迁移脚本

echo "=== 添加nickname字段到users表 ==="
echo ""

# 1. 检查字段是否已存在
echo "1. 检查nickname字段是否已存在..."
FIELD_EXISTS=$(npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "PRAGMA table_info(users)" --json | \
  jq -r '.[0].results[] | select(.name=="nickname") | .name' 2>/dev/null)

if [ "$FIELD_EXISTS" = "nickname" ]; then
    echo "   ✅ nickname字段已存在，无需添加"
    exit 0
fi

echo "   ℹ️  nickname字段不存在，准备添加..."

# 2. 备份数据
echo ""
echo "2. 备份users表数据..."
BACKUP_FILE="backup_users_before_nickname_$(date +%Y%m%d_%H%M%S).json"
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "SELECT * FROM users" --json > "$BACKUP_FILE"
echo "   ✅ 备份完成: $BACKUP_FILE"

# 3. 添加nickname字段
echo ""
echo "3. 添加nickname字段..."
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "ALTER TABLE users ADD COLUMN nickname TEXT"

if [ $? -eq 0 ]; then
    echo "   ✅ nickname字段添加成功"
else
    echo "   ❌ 添加字段失败"
    exit 1
fi

# 4. 创建索引
echo ""
echo "4. 创建索引..."
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "CREATE INDEX IF NOT EXISTS idx_users_nickname ON users(nickname)"
echo "   ✅ 索引创建完成"

# 5. 验证
echo ""
echo "5. 验证字段添加..."
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "PRAGMA table_info(users)" | grep nickname

echo ""
echo "=== 迁移完成 ==="
echo "备份文件: $BACKUP_FILE"

