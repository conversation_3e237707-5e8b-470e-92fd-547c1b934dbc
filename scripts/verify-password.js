const bcrypt = require('bcryptjs');

async function verifyPassword() {
  const password = 'qpalzm@123';
  const hash = '$2b$10$SnUQnDVWda7nt9Yvd4RHJeIhF852aYx.JLErK0YJTeZ90EB2ifmaa';
  
  const isValid = await bcrypt.compare(password, hash);
  console.log(`Password "qpalzm@123" matches hash: ${isValid}`);
  
  if (!isValid) {
    console.log('\nGenerating new hash...');
    const newHash = await bcrypt.hash(password, 10);
    console.log('New hash:', newHash);
  }
}

verifyPassword().catch(console.error);

