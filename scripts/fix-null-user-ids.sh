#!/bin/bash

# 修复用户表中id为NULL的记录
# 此脚本会为所有id为NULL的用户生成有效的UUID

echo "=== 修复用户表中的NULL ID记录 ==="
echo ""

# 检查是否有NULL id的用户
echo "1. 检查是否存在NULL id的用户..."
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "SELECT username, email FROM users WHERE id IS NULL"

# 询问是否继续
read -p "是否继续修复？(y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    echo "操作已取消"
    exit 1
fi

# 备份数据
echo ""
echo "2. 备份用户表数据..."
BACKUP_FILE="backup_users_$(date +%Y%m%d_%H%M%S).json"
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "SELECT * FROM users" --json > "$BACKUP_FILE"
echo "备份完成: $BACKUP_FILE"

# 修复NULL id
echo ""
echo "3. 修复NULL id记录..."

# 获取所有NULL id的用户
NULL_USERS=$(npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "SELECT username FROM users WHERE id IS NULL" --json | \
  jq -r '.[0].results[].username' 2>/dev/null)

if [ -z "$NULL_USERS" ]; then
    echo "没有发现NULL id的用户，无需修复"
    exit 0
fi

# 为每个用户生成UUID并更新
for username in $NULL_USERS; do
    # 生成32位十六进制UUID（模拟SQLite的hex(randomblob(16))）
    UUID=$(openssl rand -hex 16)
    
    echo "  - 为用户 '$username' 设置ID: $UUID"
    
    npx wrangler d1 execute cloudflare-admin-db --remote \
      --command "UPDATE users SET id = '$UUID' WHERE username = '$username' AND id IS NULL"
done

# 验证修复结果
echo ""
echo "4. 验证修复结果..."
npx wrangler d1 execute cloudflare-admin-db --remote \
  --command "SELECT COUNT(*) as total, COUNT(id) as with_id FROM users"

echo ""
echo "=== 修复完成 ==="
echo "备份文件: $BACKUP_FILE"

