{"clientTcpRtt": 1, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "NA", "asn": 31898, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "US", "isEUCountry": false, "region": "California", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "nByEPhIc6yxqa8cvOFiTyFBh39f+fck8cm4CuVitOUc=", "tlsExportedAuthenticator": {"clientFinished": "321d440160cb0efb0977678e447595dec994faa70ca2cc571740c69abab5cd89d542d1c3dd4ed75b8fccd64c40e8cba1", "clientHandshake": "bd472d56d299bf247de367cf7cc8a637db55d478a146f3a65d98e57a87af0594cb6d287e56f57c12296b8874b68f1553", "serverHandshake": "689d26854952a2717c6e2ba1b39d7007d42878bcbeb1b9d8227ff5188de5afa698ca66fedf0e666dc1658527a6c4184a", "serverFinished": "5e0ef06a45913be26ec4ecaab10853e7abe01e1d3b3948b4b60b0312b60e9ef96ef02e6d246a45254540adb78fcb25b3"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "America/Los_Angeles", "longitude": "-121.78750", "latitude": "37.23290", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "95119", "city": "San Jose", "tlsVersion": "TLSv1.3", "regionCode": "CA", "asOrganization": "Oracle Corporation", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}