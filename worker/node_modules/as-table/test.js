"use strict";

const assert  = require ('assert'),
      asTable = require (process.env.AS_TABLE_TEST_FILE),
      ansi    = require ('ansicolor').nice

describe ('as-table', () => {

    it ('array printing works', () => {

        var testData = [['qwe',       '123456789', 'zxcvbnm'],
                        ['qwerty',    '12',        'zxcvb'],
                        ['💩wertyiop', '1234567',   'z']]                        
                        
        assert.equal (asTable (testData),

                        'qwe        123456789  zxcvbnm\n' +
                        'qwerty     12         zxcvb  \n' +
                        '💩wertyiop  1234567    z      ')

        assert.equal (asTable.configure ({ maxTotalWidth: 22, delimiter: ' | ' }) (testData),

                        'qwe   | 1234… | zxc…\n' +
                        'qwer… | 12    | zxc…\n' +
                        '💩wer… | 1234… | z   ')

                        console.log (asTable.configure ({ maxTotalWidth: 22, delimiter: ' | ' }) (testData))
    })

    it ('object printing works', () => {

        var testData =
            [ { foo: true,  string: 'abcde',      num: 42 },
              { foo: false, string: 'qwertyuiop', num: 43 },
              {             string:  null,        num: 44 } ]

        assert.equal (asTable (testData),

            'foo    string      num\n' +
            '----------------------\n' +
            'true   abcde       42 \n' +
            'false  qwertyuiop  43 \n' +
            '       null        44 ')
    })


    it ('object printing works (with ANSI styling)', () => {

        var testData =
            [ { foo: true,  string: 'abcde'.cyan.bgYellow, num: 42 },
              { foo: false, string: 'qwertyuiop',          num: 43 },
              {             string:  null,                 num: 44 } ]

        assert.equal (asTable (testData),

            'foo    string      num\n' +
            '----------------------\n' +
            'true   \u001b[43m\u001b[36mabcde\u001b[39m\u001b[49m       42 \n' +
            'false  qwertyuiop  43 \n' +
            '       null        44 ')
    })

    it ('maxTotalWidth correctly handles object field names', () => {

        assert.equal (

            asTable.configure ({ maxTotalWidth: 15 }) ([{

                '0123456789': '0123456789',
                'abcdefxyzw': 'abcdefxyzw' }]),

            '01234…  abcde…\n' +
            '--------------\n' +
            '01234…  abcde…'
        )
    })

    it ('maxTotalWidth correctly handles object field names (with ANSI styling)', () => {

        assert.equal (

            asTable.configure ({ maxTotalWidth: 15 }) ([{

                '0123456789': '0123456789',
                'abcdefxyzw': 'abcdefxyzw'.cyan.bgYellow.italic.inverse.bright }]),

            '01234…  abcde…\n' +
            '--------------\n' +
            '01234…  ' + 'abcde'.cyan.bgYellow.italic.inverse.bright + '…'
        )
    })

    it ('everything renders as singleline', () => {

        assert.equal (asTable ([['fooo\n\nbar']]), 'fooo\\n\\nbar')
    })

    it ('configuring works', () => {

        const asTable25      = asTable.configure ({ maxTotalWidth: 25 }),
              asTable25Delim = asTable25.configure ({ delimiter: ' | ' })

        assert.notEqual (asTable25, asTable25Delim)
        assert.equal    (asTable25.maxTotalWidth, 25)
        assert.equal    (asTable25Delim.delimiter, ' | ')
    })

    it ('degenerate case works', () => {

        assert.equal (asTable ([]), '\n')
        assert.equal (asTable ([{}]), '\n\n')
    })

    it ('null/undefined prints correctly', () => {

        assert.equal (asTable.configure ({ delimiter: '|' }) ([[null, undefined, 1, 2, 3]]), 'null||1|2|3')
    })

    it ('custom printer works', () => {

        var testData =
            [ { foo: true,  string: 'abcde',      num: 42 },
              { foo: false, string: 'qwertyuiop', num: 43 },
              {             string:  null,        num: 44 } ]

        const formatsBooleansAsYesNo = asTable.configure ({ print: obj => (typeof obj === 'boolean') ? (obj ? 'yes' : 'no') : String (obj) })

        assert.equal (formatsBooleansAsYesNo (testData),

            'foo  string      num\n' +
            '--------------------\n' +
            'yes  abcde       42 \n' +
            'no   qwertyuiop  43 \n' +
            '     null        44 ')
    })

    it ('custom printer works with object titles', () => {

        var testData =
            [ { foo: true,  string: 'abcde',      num: 42, timestamp: 1561202591572 },
              { foo: false, string: 'qwertyuiop', num: 43, timestamp: 1558524240034 },
              {             string:  null,        num: 44, timestamp: 1555932240034 } ]

        const formats = asTable.configure ({
            print: (obj, title) => {
                if (title === 'foo') {
                    return obj ? 'yes' : 'no';
                }
                if (title === 'timestamp') {
                    return new Date(obj).toGMTString();
                }
                return String(obj);
            }
        })

        assert.equal (formats (testData),

            'foo  string      num  timestamp                    \n' +
            '---------------------------------------------------\n' +
            'yes  abcde       42   Sat, 22 Jun 2019 11:23:11 GMT\n' +
            'no   qwertyuiop  43   Wed, 22 May 2019 11:24:00 GMT\n' +
            '     null        44   Mon, 22 Apr 2019 11:24:00 GMT')
    })

    it ('custom printer works with array keys', () => {

        var testData =
            [ [ true,  'abcde',      42, 1561202591572 ],
              [ false, 'qwertyuiop', 43, 1558524240034 ] ]

        const formats = asTable.configure ({
            print: (obj, index) => {
                if (index === 0) {
                    return obj ? 'yes' : 'no';
                }
                if (index === 3) {
                    return new Date(obj).toGMTString();
                }
                return String(obj);
            }
        })

        assert.equal (formats (testData),

            'yes  abcde       42  Sat, 22 Jun 2019 11:23:11 GMT\n' +
            'no   qwertyuiop  43  Wed, 22 May 2019 11:24:00 GMT')
    })


    it ('right align works', () => {
        
        var testData =
            [ { foo: 1234.567,                bar: 12 },
              { foo: '4.567'.bgMagenta.green, bar: 1234.456890 } ]

        assert.equal (asTable.configure ({ right: true }) (testData),
                        '     foo         bar\n' +
                        '--------------------\n' +
                        '1234.567          12\n' +
                        '   ' + '4.567'.bgMagenta.green + '  1234.45689')
    })

    it ('ANSI coloring works', () => {

        const testData =
            [ { foo: true,  string: 'abcde',                             num: 42 },
              { foo: false, string: '💩wertyuiop'.bgMagenta.green.bright, num: 43 } ]

        const d = ' | '.dim.cyan
        const _ = '-'.bright.cyan

        const result = asTable.configure ({ title: x => x.bright, delimiter: d, dash: _ }) (testData)

        console.log (result)

        assert.equal (result,

            ['foo'.bright + '  ', 'string'.bright + '    ', 'num'.bright].join (d) + '\n' +
            _.repeat (24) + '\n' +
            ['true ', 'abcde     ', '42 '].join (d) + '\n' +
            ['false', '💩wertyuiop'.bgMagenta.green.bright, '43 '].join (d))
    })
})
