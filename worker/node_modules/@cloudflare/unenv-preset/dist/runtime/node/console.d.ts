export { Console, _ignoreErrors, _stderr, _stderr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, _stdout, _stdoutError<PERSON>andler, _times, } from "unenv/node/console";
export declare const assert: any, clear: any, context: any, count: any, countReset: any, createTask: any, debug: any, dir: any, dirxml: any, error: any, group: any, groupCollapsed: any, groupEnd: any, info: any, log: any, profile: any, profileEnd: any, table: any, time: any, timeEnd: any, timeLog: any, timeStamp: any, trace: any, warn: any;
declare const _default: any;
export default _default;
