{"name": "@fastify/busboy", "version": "2.1.1", "private": false, "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kibertoad"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/uzlopak"}], "description": "A streaming parser for HTML form data for node.js", "main": "lib/main", "type": "commonjs", "types": "lib/main.d.ts", "scripts": {"bench:busboy": "cd benchmarks && npm install && npm run benchmark-fastify", "bench:dicer": "node bench/dicer/dicer-bench-multipart-parser.js", "coveralls": "nyc report --reporter=lcov", "lint": "npm run lint:standard", "lint:everything": "npm run lint && npm run test:types", "lint:fix": "standard --fix", "lint:standard": "standard --verbose | snazzy", "test:mocha": "tap", "test:types": "tsd", "test:coverage": "nyc npm run test", "test": "npm run test:mocha"}, "engines": {"node": ">=14"}, "devDependencies": {"@types/node": "^20.1.0", "busboy": "^1.0.0", "photofinish": "^1.8.0", "snazzy": "^9.0.0", "standard": "^17.0.0", "tap": "^16.3.8", "tinybench": "^2.5.1", "tsd": "^0.30.0", "typescript": "^5.0.2"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/fastify/busboy.git"}, "tsd": {"directory": "test/types", "compilerOptions": {"esModuleInterop": false, "module": "commonjs", "target": "ES2017"}}, "standard": {"globals": ["describe", "it"], "ignore": ["bench"]}, "files": ["README.md", "LICENSE", "lib/*", "deps/encoding/*", "deps/dicer/lib", "deps/streamsearch/", "deps/dicer/LICENSE"]}