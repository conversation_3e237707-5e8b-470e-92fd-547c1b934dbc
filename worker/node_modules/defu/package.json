{"name": "defu", "version": "6.1.4", "description": "Recursively assign default properties. Lightweight and Fast!", "repository": "unjs/defu", "license": "MIT", "exports": {".": {"types": "./dist/defu.d.ts", "import": "./dist/defu.mjs", "require": "./lib/defu.cjs"}}, "main": "./lib/defu.cjs", "module": "./dist/defu.mjs", "types": "./dist/defu.d.ts", "files": ["dist", "lib"], "devDependencies": {"@types/node": "^20.10.6", "@vitest/coverage-v8": "^1.1.3", "changelogen": "^0.5.5", "eslint": "^8.56.0", "eslint-config-unjs": "^0.2.1", "expect-type": "^0.17.3", "prettier": "^3.1.1", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vitest": "^1.1.3"}, "packageManager": "pnpm@8.10.2", "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint --ext .ts src && prettier -c src test", "lint:fix": "eslint --ext .ts src --fix && prettier -w src test", "release": "pnpm test && changelogen --release && git push --follow-tags && pnpm publish", "test": "pnpm lint && pnpm vitest run", "test:types": "tsc --noEmit"}}