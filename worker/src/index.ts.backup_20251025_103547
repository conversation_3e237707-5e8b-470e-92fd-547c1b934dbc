// Cloudflare Worker API for Admin System
import bcrypt from 'bcryptjs'

interface Env {
  DB: D1Database
}

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400',
}

// Handle CORS preflight
function handleCORS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  })
}

// Response helper
function jsonResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders,
    },
  })
}

// Error response helper
function errorResponse(message: string, status = 400) {
  return jsonResponse({ error: message }, status)
}

// 从请求中获取用户信息
async function getUserFromToken(request: Request, env: Env): Promise<any | null> {
  try {
    // 从Authorization header获取token
    const authHeader = request.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }

    const token = authHeader.substring(7) // 移除 "Bearer " 前缀

    // 解析token获取用户ID (格式: token_用户ID_时间戳)
    const tokenParts = token.split('_')
    if (tokenParts.length < 3 || tokenParts[0] !== 'token') {
      return null
    }

    const userId = tokenParts[1]

    // 查询用户信息和角色
    const { results } = await env.DB.prepare(`
      SELECT u.*, r.name as role_name, r.permissions, r.description as role_description
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `).bind(userId).all()

    if (results.length === 0) {
      return null
    }

    const user = results[0] as any

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: {
        id: user.role_id,
        name: user.role_name,
        description: user.role_description,
        permissions: JSON.parse(user.permissions || '[]')
      }
    }
  } catch (error) {
    console.error('Get user from token error:', error)
    return null
  }
}

// User handlers
async function handleUsers(request: Request, env: Env, pathname: string) {
  const url = new URL(request.url)
  const method = request.method

  if (method === 'GET' && pathname === '/api/users/stats') {
    // Get users statistics
    try {
      const stats = await env.DB.prepare(`
        SELECT COUNT(*) as total
        FROM users
      `).first()

      return jsonResponse(stats)
    } catch (error) {
      console.error('Get users stats error:', error)
      return errorResponse('获取用户统计信息失败', 500)
    }
  }

  if (method === 'GET' && pathname === '/api/users') {
    // Get all users with pagination
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    const { results } = await env.DB.prepare(`
      SELECT u.*, r.name as role_name, r.permissions as role_permissions
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all()

    const { results: countResult } = await env.DB.prepare('SELECT COUNT(*) as total FROM users').all()
    const total = (countResult[0] as any).total
    const totalPages = Math.ceil(total / limit)

    const users = results.map((user: any) => ({
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      email: user.email,
      role: {
        id: user.role_id,
        name: user.role_name,
        permissions: JSON.parse(user.role_permissions || '[]')
      },
      last_login_at: user.last_login_at,
      last_active_at: user.last_active_at,
      created_at: user.created_at,
      updated_at: user.updated_at
    }))

    return jsonResponse({
      data: users,
      total,
      page,
      limit,
      totalPages
    })
  }

  if (method === 'POST' && pathname === '/api/users') {
    // Create user
    const data = await request.json() as any
    const { username, nickname, email, password, roleId, role_id } = data

    // Handle both roleId (frontend) and role_id (database) field names
    const finalRoleId = roleId || role_id

    // Hash password
    const password_hash = await bcrypt.hash(password, 10)

    // 生成UUID（使用crypto.randomUUID()或手动生成）
    // 由于D1的默认值生成有问题，我们手动生成UUID
    const generateUUID = () => {
      const hex = Array.from(crypto.getRandomValues(new Uint8Array(16)))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
      return hex
    }

    const userId = generateUUID()

    const { results } = await env.DB.prepare(`
      INSERT INTO users (id, username, nickname, email, password_hash, role_id)
      VALUES (?, ?, ?, ?, ?, ?)
      RETURNING *
    `).bind(userId, username, nickname || null, email, password_hash, finalRoleId).all()

    return jsonResponse(results[0])
  }

  // Handle user by ID
  const userIdMatch = pathname.match(/^\/api\/users\/([^\/]+)$/)
  if (userIdMatch) {
    const userId = userIdMatch[1]

    if (method === 'GET') {
      const { results } = await env.DB.prepare(`
        SELECT u.*, r.name as role_name, r.permissions as role_permissions
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        WHERE u.id = ?
      `).bind(userId).all()

      if (results.length === 0) {
        return errorResponse('User not found', 404)
      }

      const user = results[0] as any
      return jsonResponse({
        id: user.id,
        username: user.username,
        email: user.email,
        role: {
          id: user.role_id,
          name: user.role_name,
          permissions: JSON.parse(user.role_permissions || '[]')
        },
        created_at: user.created_at,
        updated_at: user.updated_at
      })
    }

    if (method === 'PUT') {
      const data = await request.json() as any
      const updates: string[] = []
      const values: any[] = []

      if (data.username) {
        updates.push('username = ?')
        values.push(data.username)
      }
      if (data.nickname !== undefined) {
        updates.push('nickname = ?')
        values.push(data.nickname || null)
      }
      if (data.email) {
        updates.push('email = ?')
        values.push(data.email)
      }
      if (data.password) {
        updates.push('password_hash = ?')
        values.push(await bcrypt.hash(data.password, 10))
      }
      if (data.role_id || data.roleId) {
        updates.push('role_id = ?')
        values.push(data.role_id || data.roleId)
      }

      updates.push('updated_at = CURRENT_TIMESTAMP')
      values.push(userId)

      const { results } = await env.DB.prepare(`
        UPDATE users SET ${updates.join(', ')} WHERE id = ? RETURNING *
      `).bind(...values).all()

      return jsonResponse(results[0])
    }

    if (method === 'DELETE') {
      // 验证用户身份和权限
      const currentUser = await getUserFromToken(request, env)
      if (!currentUser) {
        return errorResponse('未授权访问', 401)
      }

      // 检查是否有删除用户的权限
      const permissions = currentUser.role?.permissions || []
      const isSuperAdmin = permissions.includes('admin')
      const hasDeletePermission = permissions.includes('users:delete')

      if (!isSuperAdmin && !hasDeletePermission) {
        return errorResponse('无权限删除用户', 403)
      }

      // 检查要删除的用户信息
      const { results: userResults } = await env.DB.prepare(`
        SELECT username FROM users WHERE id = ?
      `).bind(userId).all()

      if (userResults.length === 0) {
        return errorResponse('用户不存在', 404)
      }

      const targetUser = userResults[0] as any

      // 不允许删除admin用户
      if (targetUser.username === 'admin') {
        return errorResponse('不能删除系统管理员账户', 403)
      }

      // 不允许删除自己
      if (userId === currentUser.id) {
        return errorResponse('不能删除当前登录的账户', 403)
      }

      try {
        // 执行删除操作（外键约束会自动级联删除相关的sessions和augments）
        const result = await env.DB.prepare('DELETE FROM users WHERE id = ?').bind(userId).run()

        if (result.changes === 0) {
          return errorResponse('删除失败，用户不存在', 404)
        }

        return jsonResponse({
          success: true,
          message: `用户 "${targetUser.username}" 已成功删除`
        })
      } catch (error: any) {
        console.error('Delete user error:', error)
        return errorResponse(`删除用户失败: ${error.message}`, 500)
      }
    }
  }

  return errorResponse('Not found', 404)
}

// Auth handlers
async function handleAuth(request: Request, env: Env, pathname: string) {
  const url = new URL(request.url)
  const method = request.method

  if (method === 'POST' && pathname === '/api/auth/login') {
    try {
      const { username, password } = await request.json()

      // 查询用户
      const { results } = await env.DB.prepare(`
        SELECT u.*, r.name as role_name, r.permissions, r.description as role_description,
               r.created_at as role_created_at, r.updated_at as role_updated_at
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        WHERE u.username = ?
      `).bind(username).all()

      if (results.length === 0) {
        return errorResponse('用户不存在', 401)
      }

      const user = results[0] as any

      // 验证密码 (这里应该使用bcrypt，但为了简化先用明文比较)
      const isValidPassword = await bcrypt.compare(password, user.password_hash)

      if (!isValidPassword) {
        return errorResponse('密码错误', 401)
      }

      // 更新最后登录时间
      const now = new Date().toISOString()
      await env.DB.prepare(
        'UPDATE users SET last_login_at = ? WHERE id = ?'
      ).bind(now, user.id).run()

      // 构造用户对象
      const authUser = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: {
          id: user.role_id,
          name: user.role_name,
          description: user.role_description,
          permissions: JSON.parse(user.permissions || '[]'),
          created_at: user.role_created_at,
          updated_at: user.role_updated_at
        },
        created_at: user.created_at,
        updated_at: user.updated_at
      }

      // 生成简单的token (在实际应用中应该使用JWT)
      const token = `token_${user.id}_${Date.now()}`

      return jsonResponse({
        user: authUser,
        token
      })
    } catch (error) {
      console.error('Login error:', error)
      return errorResponse('登录失败', 500)
    }
  }

  return errorResponse('Not found', 404)
}

// Role handlers
async function handleRoles(request: Request, env: Env, pathname: string) {
  const url = new URL(request.url)
  const method = request.method

  if (method === 'GET' && pathname === '/api/roles/stats') {
    // Get roles statistics
    try {
      const stats = await env.DB.prepare(`
        SELECT COUNT(*) as total
        FROM roles
      `).first()

      return jsonResponse(stats)
    } catch (error) {
      console.error('Get roles stats error:', error)
      return errorResponse('获取角色统计信息失败', 500)
    }
  }

  if (method === 'GET' && pathname === '/api/roles') {
    // Get all roles with pagination
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    const { results } = await env.DB.prepare(`
      SELECT * FROM roles
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all()

    const { results: countResult } = await env.DB.prepare('SELECT COUNT(*) as total FROM roles').all()
    const total = (countResult[0] as any).total
    const totalPages = Math.ceil(total / limit)

    const roles = results.map((role: any) => ({
      ...role,
      permissions: JSON.parse(role.permissions || '[]')
    }))

    return jsonResponse({
      data: roles,
      total,
      page,
      limit,
      totalPages
    })
  }

  if (method === 'POST' && pathname === '/api/roles') {
    try {
      const { name, description, permissions } = await request.json()

      // 生成ID
      const id = `role-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const now = new Date().toISOString().replace('T', ' ').substr(0, 19)

      // 插入角色
      await env.DB.prepare(`
        INSERT INTO roles (id, name, description, permissions, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        id,
        name,
        description || '',
        JSON.stringify(permissions || []),
        now,
        now
      ).run()

      // 返回创建的角色
      const role = {
        id,
        name,
        description: description || '',
        permissions: permissions || [],
        created_at: now,
        updated_at: now
      }

      return jsonResponse(role, 201)
    } catch (error) {
      console.error('Create role error:', error)
      return errorResponse('创建角色失败', 500)
    }
  }

  return errorResponse('Not found', 404)
}

// Augment handlers
async function handleAugments(request: Request, env: Env, pathname: string) {
  const url = new URL(request.url)
  const method = request.method

  // 验证用户身份
  const currentUser = await getUserFromToken(request, env)
  if (!currentUser) {
    return errorResponse('未授权访问', 401)
  }

  // 检查是否是超级管理员
  const isSuperAdmin = currentUser.role.name === '超级管理员'

  if (method === 'GET' && pathname === '/api/augments') {
    // Get all augments with pagination
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    try {
      // 更新用户最近使用时间
      const now = new Date().toISOString()
      await env.DB.prepare(
        'UPDATE users SET last_active_at = ? WHERE id = ?'
      ).bind(now, currentUser.id).run()

      // 根据用户角色构建查询
      let query = `
        SELECT
          a.id,
          a.tenant_url,
          a.access_token,
          a.portal_url,
          a.email,
          a.auth_session,
          a.user_id,
          a.status,
          a.price,
          a.created_at,
          a.updated_at,
          u.username as user_name,
          u.nickname as user_nickname
        FROM augments a
        LEFT JOIN users u ON a.user_id = u.id
      `

      let countQuery = 'SELECT COUNT(*) as count FROM augments'
      const queryParams: any[] = []
      const countParams: any[] = []

      // 如果不是超级管理员，只查询自己的数据
      if (!isSuperAdmin) {
        query += ' WHERE a.user_id = ?'
        countQuery += ' WHERE user_id = ?'
        queryParams.push(currentUser.id)
        countParams.push(currentUser.id)
      }

      query += ' ORDER BY a.created_at DESC LIMIT ? OFFSET ?'
      queryParams.push(limit, offset)

      // Get augments with user info
      const { results } = await env.DB.prepare(query).bind(...queryParams).all()

      // Get total count
      const { count } = await env.DB.prepare(countQuery).bind(...countParams).first() as { count: number }

      const totalPages = Math.ceil(count / limit)

      return jsonResponse({
        data: results,
        total: count,
        page,
        limit,
        totalPages
      })
    } catch (error) {
      console.error('Get augments error:', error)
      return errorResponse('获取Augment列表失败', 500)
    }
  }

  if (method === 'GET' && pathname === '/api/augments/stats') {
    // Get augments statistics
    try {
      // 根据用户角色构建查询
      let query = `
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'used' THEN 1 END) as used_count,
          COUNT(CASE WHEN status = 'unused' THEN 1 END) as unused_count,
          COALESCE(SUM(CASE WHEN status = 'used' THEN price ELSE 0 END), 0) as used_revenue,
          COALESCE(SUM(price), 0) as total_revenue
        FROM augments
      `

      const queryParams: any[] = []

      // 如果不是超级管理员，只统计自己的数据
      if (!isSuperAdmin) {
        query += ' WHERE user_id = ?'
        queryParams.push(currentUser.id)
      }

      const stats = queryParams.length > 0
        ? await env.DB.prepare(query).bind(...queryParams).first()
        : await env.DB.prepare(query).first()

      return jsonResponse(stats)
    } catch (error) {
      console.error('Get stats error:', error)
      return errorResponse('获取统计信息失败', 500)
    }
  }

  if (method === 'POST' && pathname === '/api/augments') {
    // Create new augment
    try {
      const data = await request.json()
      const { tenant_url, access_token, portal_url, email, auth_session, user_id, price } = data

      if (!tenant_url || !access_token || !user_id) {
        return errorResponse('缺少必填字段', 400)
      }

      // 检查access_token是否已存在
      const existing = await env.DB.prepare(
        'SELECT COUNT(*) as count FROM augments WHERE access_token = ?'
      ).bind(access_token).first() as { count: number }

      if (existing && existing.count > 0) {
        return errorResponse('该Access Token已存在', 400)
      }

      const id = crypto.randomUUID()
      const now = new Date().toISOString()
      const status = 'unused' // 默认状态为未使用
      const augmentPrice = price || 0.00 // 默认价格为0

      await env.DB.prepare(`
        INSERT INTO augments (id, tenant_url, access_token, portal_url, email, auth_session, user_id, status, price, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(id, tenant_url, access_token, portal_url, email, auth_session, user_id, status, augmentPrice, now, now).run()

      const augment = {
        id,
        tenant_url,
        access_token,
        portal_url: portal_url || null,
        email: email || null,
        auth_session: auth_session || null,
        user_id,
        status,
        price: augmentPrice,
        created_at: now,
        updated_at: now
      }

      return jsonResponse(augment, 201)
    } catch (error) {
      console.error('Create augment error:', error)
      return errorResponse('创建Augment失败', 500)
    }
  }

  // Handle batch delete (only for super admin)
  if (method === 'DELETE' && pathname === '/api/augments/batch') {
    // 验证是否是超级管理员
    if (!isSuperAdmin) {
      return errorResponse('无权限执行批量删除操作', 403)
    }

    try {
      const data = await request.json() as { ids: string[] }

      if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
        return errorResponse('请提供要删除的Augment ID列表', 400)
      }

      // 限制一次最多删除100个
      if (data.ids.length > 100) {
        return errorResponse('一次最多删除100个Augment', 400)
      }

      // 构建批量删除SQL
      const placeholders = data.ids.map(() => '?').join(',')
      const result = await env.DB.prepare(
        `DELETE FROM augments WHERE id IN (${placeholders})`
      ).bind(...data.ids).run()

      return jsonResponse({
        message: '批量删除成功',
        deletedCount: result.meta.changes || 0
      })
    } catch (error) {
      console.error('Batch delete error:', error)
      return errorResponse('批量删除失败', 500)
    }
  }

  // Handle batch assign user (only for super admin)
  if (method === 'PUT' && pathname === '/api/augments/batch-assign') {
    // 验证是否是超级管理员
    if (!isSuperAdmin) {
      return errorResponse('无权限执行批量分配操作', 403)
    }

    try {
      const data = await request.json() as { ids: string[], userId: string, price?: number }

      if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
        return errorResponse('请提供要分配的Augment ID列表', 400)
      }

      if (!data.userId) {
        return errorResponse('请提供要分配的用户ID', 400)
      }

      // 限制一次最多分配100个
      if (data.ids.length > 100) {
        return errorResponse('一次最多分配100个Augment', 400)
      }

      // 验证用户是否存在
      const userCheck = await env.DB.prepare(
        'SELECT id FROM users WHERE id = ?'
      ).bind(data.userId).first()

      if (!userCheck) {
        return errorResponse('指定的用户不存在', 404)
      }

      // 构建批量更新SQL
      const placeholders = data.ids.map(() => '?').join(',')
      let sql: string
      let bindParams: any[]

      if (data.price !== undefined && data.price !== null) {
        // 如果提供了价格,则更新价格
        sql = `UPDATE augments SET user_id = ?, price = ?, updated_at = datetime('now') WHERE id IN (${placeholders})`
        bindParams = [data.userId, data.price, ...data.ids]
      } else {
        // 如果没有提供价格,则只更新用户ID
        sql = `UPDATE augments SET user_id = ?, updated_at = datetime('now') WHERE id IN (${placeholders})`
        bindParams = [data.userId, ...data.ids]
      }

      const result = await env.DB.prepare(sql).bind(...bindParams).run()

      return jsonResponse({
        message: '批量分配成功',
        assignedCount: result.meta.changes || 0
      })
    } catch (error) {
      console.error('Batch assign error:', error)
      return errorResponse('批量分配失败', 500)
    }
  }

  // Handle batch import (only for super admin)
  if (method === 'POST' && pathname === '/api/augments/batch-import') {
    // 验证是否是超级管理员
    if (!isSuperAdmin) {
      return errorResponse('无权限执行批量导入操作', 403)
    }

    try {
      const data = await request.json() as { content: string }

      if (!data.content || typeof data.content !== 'string') {
        return errorResponse('请提供要导入的内容', 400)
      }

      // 查找admin用户
      const adminUser = await env.DB.prepare(
        'SELECT id FROM users WHERE username = ?'
      ).bind('admin').first() as { id: string } | null

      if (!adminUser) {
        return errorResponse('未找到admin用户', 404)
      }

      // 解析文本内容
      const lines = data.content.split('\n').map(line => line.trim()).filter(line => line.length > 0)

      if (lines.length % 4 !== 0) {
        return errorResponse('数据格式错误,每4行为一组数据', 400)
      }

      const augments: Array<{
        tenant_url: string
        access_token: string
        portal_url: string
        email: string
        auth_session?: string
      }> = []
      for (let i = 0; i < lines.length; i += 4) {
        augments.push({
          tenant_url: lines[i],
          access_token: lines[i + 1],
          portal_url: lines[i + 2],
          email: lines[i + 3],
          auth_session: undefined
        })
      }

      // 批量插入数据库
      let successCount = 0
      let skipCount = 0
      let failCount = 0

      for (const augment of augments) {
        try {
          // 检查access_token是否已存在
          const existing = await env.DB.prepare(
            'SELECT COUNT(*) as count FROM augments WHERE access_token = ?'
          ).bind(augment.access_token).first() as { count: number }

          if (existing && existing.count > 0) {
            skipCount++
            continue
          }

          // 插入数据
          await env.DB.prepare(
            `INSERT INTO augments (id, tenant_url, access_token, portal_url, email, auth_session, user_id, status, price, created_at, updated_at)
             VALUES (lower(hex(randomblob(16))), ?, ?, ?, ?, ?, ?, 'unused', 0.00, datetime('now'), datetime('now'))`
          ).bind(
            augment.tenant_url,
            augment.access_token,
            augment.portal_url,
            augment.email,
            augment.auth_session || null,
            adminUser.id
          ).run()
          successCount++
        } catch (error) {
          console.error('Import error for augment:', augment, error)
          failCount++
        }
      }

      return jsonResponse({
        message: '批量导入完成',
        success: successCount,
        skipped: skipCount,
        failed: failCount,
        total: augments.length
      })
    } catch (error) {
      console.error('Batch import error:', error)
      return errorResponse('批量导入失败', 500)
    }
  }

  // Handle individual augment operations
  const augmentIdMatch = pathname.match(/^\/api\/augments\/([^\/]+)(?:\/.*)?$/)
  if (augmentIdMatch) {
    const augmentId = augmentIdMatch[1]

    if (method === 'PUT') {
      // Update augment
      try {
        const data = await request.json()
        const updates = []
        const values = []

        if (data.tenant_url) {
          updates.push('tenant_url = ?')
          values.push(data.tenant_url)
        }
        if (data.access_token) {
          updates.push('access_token = ?')
          values.push(data.access_token)
        }
        if (data.portal_url !== undefined) {
          updates.push('portal_url = ?')
          values.push(data.portal_url)
        }
        if (data.email !== undefined) {
          updates.push('email = ?')
          values.push(data.email)
        }
        if (data.auth_session !== undefined) {
          updates.push('auth_session = ?')
          values.push(data.auth_session)
        }
        if (data.user_id) {
          updates.push('user_id = ?')
          values.push(data.user_id)
        }
        if (data.status !== undefined) {
          updates.push('status = ?')
          values.push(data.status)
        }
        if (data.price !== undefined) {
          updates.push('price = ?')
          values.push(data.price)
        }

        if (updates.length === 0) {
          return errorResponse('没有要更新的字段', 400)
        }

        updates.push('updated_at = ?')
        values.push(new Date().toISOString())
        values.push(augmentId)

        await env.DB.prepare(`
          UPDATE augments
          SET ${updates.join(', ')}
          WHERE id = ?
        `).bind(...values).run()

        // Get updated augment
        const augment = await env.DB.prepare(`
          SELECT
            a.*,
            u.username as user_name
          FROM augments a
          LEFT JOIN users u ON a.user_id = u.id
          WHERE a.id = ?
        `).bind(augmentId).first()

        if (!augment) {
          return errorResponse('Augment不存在', 404)
        }

        return jsonResponse(augment)
      } catch (error) {
        console.error('Update augment error:', error)
        return errorResponse('更新Augment失败', 500)
      }
    }

    if (method === 'PATCH' && pathname.endsWith('/toggle-status')) {
      // Toggle augment status
      try {
        // Get current status
        const current = await env.DB.prepare('SELECT status FROM augments WHERE id = ?').bind(augmentId).first() as { status: string } | null

        if (!current) {
          return errorResponse('Augment不存在', 404)
        }

        const newStatus = current.status === 'used' ? 'unused' : 'used'
        const now = new Date().toISOString()

        await env.DB.prepare(`
          UPDATE augments
          SET status = ?, updated_at = ?
          WHERE id = ?
        `).bind(newStatus, now, augmentId).run()

        // Get updated augment
        const augment = await env.DB.prepare(`
          SELECT
            a.*,
            u.username as user_name
          FROM augments a
          LEFT JOIN users u ON a.user_id = u.id
          WHERE a.id = ?
        `).bind(augmentId).first()

        return jsonResponse(augment)
      } catch (error) {
        console.error('Toggle status error:', error)
        return errorResponse('切换状态失败', 500)
      }
    }

    if (method === 'DELETE') {
      // Delete augment
      try {
        const result = await env.DB.prepare('DELETE FROM augments WHERE id = ?').bind(augmentId).run()

        if (result.changes === 0) {
          return errorResponse('Augment不存在', 404)
        }

        return jsonResponse({ message: 'Augment删除成功' })
      } catch (error) {
        console.error('Delete augment error:', error)
        return errorResponse('删除Augment失败', 500)
      }
    }
  }

  return errorResponse('Not found', 404)
}

export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url)
    const pathname = url.pathname

    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return handleCORS()
    }

    try {
      // Route to appropriate handler
      if (pathname.startsWith('/api/users')) {
        return await handleUsers(request, env, pathname)
      }

      if (pathname.startsWith('/api/roles')) {
        return await handleRoles(request, env, pathname)
      }

      if (pathname.startsWith('/api/auth')) {
        return await handleAuth(request, env, pathname)
      }

      if (pathname.startsWith('/api/augments')) {
        return await handleAugments(request, env, pathname)
      }

      // Health check
      if (pathname === '/health') {
        return jsonResponse({ status: 'ok', timestamp: new Date().toISOString() })
      }

      return errorResponse('Not found', 404)
    } catch (error) {
      console.error('Worker error:', error)
      return errorResponse('Internal server error', 500)
    }
  },
}
