# Cloudflare 后台管理系统 API 文档

## 基础信息

**前端地址:** `https://admin.liubao.site`

**API Base URL:** `https://api-admin.liubao.site`

**认证方式:** Bearer Token

所有需要认证的接口都需要在请求头中添加:
```
Authorization: Bearer YOUR_TOKEN
```

---

## 1. 用户登录接口

### 接口地址
```
POST /api/login
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

### 请求示例

```bash
curl -X POST https://api-admin.liubao.site/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "your_password"
  }'
```

### 响应示例

**成功响应 (200):**
```json
{
  "token": "token_demo-admin-id_1759389713145",
  "user": {
    "id": "demo-admin-id",
    "username": "admin",
    "email": "<EMAIL>",
    "role": {
      "id": "1",
      "name": "超级管理员"
    },
    "permissions": [
      {
        "id": "1",
        "name": "users:read",
        "description": "查看用户"
      },
      {
        "id": "2",
        "name": "users:write",
        "description": "管理用户"
      }
      // ... 更多权限
    ]
  }
}
```

**失败响应 (401):**
```json
{
  "error": "用户名或密码错误"
}
```

### 说明
- 登录成功后会返回 token 和用户信息
- token 需要保存并在后续请求中使用
- token 格式: `token_{userId}_{timestamp}`

---

## 2. Augment 批量导入接口

### 接口地址
```
POST /api/augments/batch-import
```

### 权限要求
- 仅超级管理员可以访问

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| content | string | 是 | 批量导入的文本内容,每4行为一组数据 |

### 数据格式说明

每4行为一组数据,格式如下:
```
第1行: tenant_url (租户URL)
第2行: access_token (访问令牌)
第3行: portal_url (门户URL)
第4行: email (邮箱)
```

### 请求示例

```bash
curl -X POST https://api-admin.liubao.site/api/augments/batch-import \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer token_demo-admin-id_1759389713145" \
  -d '{
    "content": "https://tenant1.example.com\ntoken123456\nhttps://portal1.example.com\<EMAIL>\nhttps://tenant2.example.com\ntoken789012\nhttps://portal2.example.com\<EMAIL>"
  }'
```

### 完整示例 (多组数据)

```bash
curl -X POST https://api-admin.liubao.site/api/augments/batch-import \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "https://d1.api.augmentcode.com/\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\nhttps://portal1.augmentcode.com/\<EMAIL>\nhttps://d2.api.augmentcode.com/\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\nhttps://portal2.augmentcode.com/\<EMAIL>\nhttps://d3.api.augmentcode.com/\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\nhttps://portal3.augmentcode.com/\<EMAIL>"
  }'
```

### 响应示例

**成功响应 (200):**
```json
{
  "message": "批量导入成功",
  "importedCount": 3,
  "totalCount": 3
}
```

**失败响应 (400 - 数据格式错误):**
```json
{
  "error": "数据格式错误,每4行为一组数据"
}
```

**失败响应 (403 - 权限不足):**
```json
{
  "error": "无权限执行批量导入操作"
}
```

**失败响应 (404 - 未找到admin用户):**
```json
{
  "error": "未找到admin用户"
}
```

### 说明
- 导入的数据会自动分配给 admin 用户
- 价格默认为 0
- 状态默认为 unused
- 如果某条数据导入失败,会继续导入其他数据
- 返回的 importedCount 是实际成功导入的数量
- 返回的 totalCount 是解析出的总数量

### 数据示例

假设要导入3个Augment,文本内容应该是:
```
https://d1.api.augmentcode.com/
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0.dozjgNryP4J3jVmNHl0w5N_XgL0n3I9PlFUP0THsR8U
https://portal1.augmentcode.com/
<EMAIL>
https://d2.api.augmentcode.com/
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkxIn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
https://portal2.augmentcode.com/
<EMAIL>
https://d3.api.augmentcode.com/
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkyIn0.4pcPyMD09olPSyXnrXCjTwXyr4BsezdI1AVTmud2fU4
https://portal3.augmentcode.com/
<EMAIL>
```

---

## 3. 其他常用接口

### 3.1 获取Augment列表

```
GET /api/augments?page=1&limit=10
```

**权限要求:** 需要登录

**响应示例:**
```json
{
  "data": [
    {
      "id": "8ab0858a-a0bb-4e97-8b58-d8a8f6b1cb38",
      "tenant_url": "https://d6.api.augmentcode.com/",
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "portal_url": "https://portal.augmentcode.com/",
      "email": "<EMAIL>",
      "user_id": "demo-admin-id",
      "status": "unused",
      "price": 0,
      "created_at": "2025-01-01 12:00:00",
      "updated_at": "2025-01-01 12:00:00"
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 10
}
```

### 3.2 批量删除Augment (仅超级管理员)

```
DELETE /api/augments/batch
```

**请求参数:**
```json
{
  "ids": ["id1", "id2", "id3"]
}
```

**响应示例:**
```json
{
  "message": "批量删除成功",
  "deletedCount": 3
}
```

### 3.3 批量分配用户 (仅超级管理员)

```
PUT /api/augments/batch-assign
```

**请求参数:**
```json
{
  "ids": ["id1", "id2", "id3"],
  "userId": "user-id-123"
}
```

**响应示例:**
```json
{
  "message": "批量分配成功",
  "assignedCount": 3
}
```

---

## 错误码说明

| HTTP状态码 | 说明 |
|-----------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 注意事项

1. 所有接口都需要使用 HTTPS
2. 请求头必须包含 `Content-Type: application/json`
3. 需要认证的接口必须在请求头中包含 `Authorization: Bearer YOUR_TOKEN`
4. Token 在登录后获取,需要妥善保管
5. 批量操作接口限制一次最多处理100条数据
6. 超级管理员拥有所有权限,普通用户只能查看和管理自己的数据

---

## 联系方式

如有问题,请联系系统管理员。

