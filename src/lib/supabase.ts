import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// 检查环境变量是否正确配置
const isConfigured = !!(supabaseUrl && supabaseAnonKey && supabaseServiceKey &&
                       supabaseUrl.startsWith('http') &&
                       supabaseUrl !== 'your_supabase_url_here')

if (!isConfigured && typeof window === 'undefined') {
  console.warn('⚠️  Supabase 环境变量未配置，应用将运行在演示模式下')
}

// 创建mock客户端用于演示模式
const createMockClient = () => ({
  from: () => ({
    select: () => ({ data: [], error: null }),
    insert: () => ({ data: null, error: null }),
    update: () => ({ data: null, error: null }),
    delete: () => ({ data: null, error: null }),
    eq: () => ({ data: null, error: null }),
    single: () => ({ data: null, error: null }),
    range: () => ({ data: [], error: null, count: 0 }),
    order: () => ({ data: [], error: null })
  })
})

// 客户端实例（用于前端）
export const supabase = isConfigured
  ? createClient(supabaseUrl!, supabaseAnonKey!)
  : (createMockClient() as unknown as ReturnType<typeof createClient>)

// 服务端实例（用于API路由，具有管理员权限）
export const supabaseAdmin = isConfigured
  ? createClient(supabaseUrl!, supabaseServiceKey!, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : (createMockClient() as unknown as ReturnType<typeof createClient>)

export { isConfigured }
