import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string): string {
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

/**
 * 计算Augment剩余时间
 * @param createdAt 创建时间
 * @returns 剩余时间信息
 */
export function calculateRemainingTime(createdAt: string): {
  days: number
  hours: number
  minutes: number
  isExpired: boolean
} {
  const VALID_DAYS = 7 // 有效期7天
  const createdTime = new Date(createdAt).getTime()
  const expireTime = createdTime + VALID_DAYS * 24 * 60 * 60 * 1000
  const currentTime = new Date().getTime()
  const remainingMs = expireTime - currentTime

  if (remainingMs <= 0) {
    return { days: 0, hours: 0, minutes: 0, isExpired: true }
  }

  const days = Math.floor(remainingMs / (24 * 60 * 60 * 1000))
  const hours = Math.floor((remainingMs % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
  const minutes = Math.floor((remainingMs % (60 * 60 * 1000)) / (60 * 1000))

  return { days, hours, minutes, isExpired: false }
}

/**
 * 格式化剩余时间显示
 * @param remainingTime 剩余时间信息
 * @returns 格式化的字符串
 */
export function formatRemainingTime(remainingTime: {
  days: number
  hours: number
  minutes: number
  isExpired: boolean
}): string {
  if (remainingTime.isExpired) {
    return '已过期'
  }

  const { days, hours, minutes } = remainingTime
  return `${days}天${hours}时${minutes}分`
}

/**
 * 格式化相对时间
 * @param dateTime ISO 8601格式的时间字符串
 * @param neverText 如果时间为null时显示的文本
 * @returns 相对时间字符串
 */
export function formatRelativeTime(dateTime: string | null, neverText: string = '从未'): string {
  if (!dateTime) {
    return neverText
  }

  const now = new Date().getTime()
  const targetTime = new Date(dateTime).getTime()
  const diffMs = now - targetTime

  // 如果是未来时间,返回完整时间
  if (diffMs < 0) {
    return formatDate(dateTime)
  }

  const seconds = Math.floor(diffMs / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const weeks = Math.floor(days / 7)
  const months = Math.floor(days / 30)
  const years = Math.floor(days / 365)

  if (seconds < 60) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else if (weeks < 4) {
    return `${weeks}周前`
  } else if (months < 12) {
    return `${months}个月前`
  } else {
    return `${years}年前`
  }
}

/**
 * 格式化完整时间
 * @param dateTime ISO 8601格式的时间字符串
 * @returns 格式化的完整时间字符串
 */
export function formatFullTime(dateTime: string | null): string {
  if (!dateTime) {
    return '-'
  }

  const d = new Date(dateTime)
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
