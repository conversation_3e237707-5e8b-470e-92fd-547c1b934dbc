import { User, Role, Augment, Session, PaginatedResponse } from './types'

// 类型定义
interface CreateUserData {
  username: string
  email: string
  password: string
  role_id: string
}

interface UpdateUserData {
  username?: string
  email?: string
  password?: string
  role_id?: string
}

interface CreateRoleData {
  name: string
  description?: string
  permissions: string[]
}

interface UpdateRoleData {
  name?: string
  description?: string
  permissions?: string[]
}

interface CreateAugmentData {
  tenant_url: string
  access_token: string
  portal_url?: string
  email?: string
  auth_session?: string
  user_id: string
}

interface UpdateAugmentData {
  tenant_url?: string
  access_token?: string
  portal_url?: string
  email?: string
  auth_session?: string
  user_id?: string
}

interface CreateSessionData {
  user_id: string
  token: string
  expires_at: Date
}

// 演示模式数据
const demoUsers: User[] = [
  {
    id: 'demo-admin-id',
    username: 'admin',
    email: '<EMAIL>',
    password_hash: '$2b$10$usyaYf3zYKymIU9yTTJ9Q.oCo4FqhB.gckA/QYtANWCW08W/6R2Dq',
    role_id: 'demo-role-id',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    role: {
      id: 'demo-role-id',
      name: '超级管理员',
      permissions: ['admin'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }
]

const demoRoles: Role[] = [
  {
    id: 'demo-role-id',
    name: '超级管理员',
    permissions: ['admin'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

const demoAugments: Augment[] = []

// 演示模式用户服务
export const demoUserService = {
  async getAll(page = 1, limit = 10): Promise<PaginatedResponse<User>> {
    return {
      data: demoUsers,
      total: demoUsers.length,
      page,
      limit,
      totalPages: Math.ceil(demoUsers.length / limit)
    }
  },

  async getById(id: string): Promise<User | null> {
    return demoUsers.find(user => user.id === id) || null
  },

  async getByUsername(username: string): Promise<User | null> {
    return demoUsers.find(user => user.username === username) || null
  },

  async create(_userData: CreateUserData): Promise<User> {
    throw new Error('演示模式不支持创建用户')
  },

  async update(_id: string, _userData: UpdateUserData): Promise<User> {
    throw new Error('演示模式不支持更新用户')
  },

  async delete(_id: string): Promise<void> {
    throw new Error('演示模式不支持删除用户')
  }
}

// 演示模式角色服务
export const demoRoleService = {
  async getAll(page = 1, limit = 10): Promise<PaginatedResponse<Role>> {
    return {
      data: demoRoles,
      total: demoRoles.length,
      page,
      limit,
      totalPages: Math.ceil(demoRoles.length / limit)
    }
  },

  async getById(id: string): Promise<Role | null> {
    return demoRoles.find(role => role.id === id) || null
  },

  async create(_roleData: CreateRoleData): Promise<Role> {
    throw new Error('演示模式不支持创建角色')
  },

  async update(_id: string, _roleData: UpdateRoleData): Promise<Role> {
    throw new Error('演示模式不支持更新角色')
  },

  async delete(_id: string): Promise<void> {
    throw new Error('演示模式不支持删除角色')
  }
}

// 演示模式Augment服务
export const demoAugmentService = {
  async getAll(page = 1, limit = 10): Promise<PaginatedResponse<Augment>> {
    return {
      data: demoAugments,
      total: demoAugments.length,
      page,
      limit,
      totalPages: Math.ceil(demoAugments.length / limit)
    }
  },

  async getById(id: string): Promise<Augment | null> {
    return demoAugments.find(augment => augment.id === id) || null
  },

  async create(_augmentData: CreateAugmentData): Promise<Augment> {
    throw new Error('演示模式不支持创建Augment')
  },

  async update(_id: string, _augmentData: UpdateAugmentData): Promise<Augment> {
    throw new Error('演示模式不支持更新Augment')
  },

  async delete(_id: string): Promise<void> {
    throw new Error('演示模式不支持删除Augment')
  }
}

// 演示模式会话服务
export const demoSessionService = {
  async create(sessionData: CreateSessionData): Promise<Session> {
    // 演示模式不需要真正的会话存储
    return {
      id: 'demo-session-id',
      user_id: sessionData.user_id,
      token: sessionData.token,
      expires_at: sessionData.expires_at.toISOString(),
      created_at: new Date().toISOString()
    }
  },

  async getByToken(_token: string): Promise<Session | null> {
    // 演示模式总是返回有效会话
    return {
      id: 'demo-session-id',
      user_id: 'demo-admin-id',
      token: _token,
      expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString()
    }
  },

  async deleteByToken(_token: string): Promise<void> {
    // 演示模式不需要删除会话
  },

  async deleteExpired(): Promise<void> {
    // 演示模式不需要清理过期会话
  }
}
