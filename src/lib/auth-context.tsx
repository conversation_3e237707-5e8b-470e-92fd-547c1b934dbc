'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { AuthUser } from './auth'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  login: (username: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  hasPermission: (permission: string) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 演示模式的管理员用户
const DEMO_ADMIN: AuthUser = {
  id: 'demo-admin-id',
  username: 'admin',
  email: '<EMAIL>',
  role: {
    id: 'admin-role-id',
    name: '超级管理员',
    description: '拥有所有权限的管理员角色',
    permissions: ['admin', 'users:read', 'users:write', 'roles:read', 'roles:write', 'augments:read', 'augments:write'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // 检查本地存储的登录状态
  const checkAuthStatus = () => {
    try {
      const token = localStorage.getItem('auth_token')
      const userData = localStorage.getItem('auth_user')

      if (token && userData) {
        const parsedUser = JSON.parse(userData)
        setUser(parsedUser)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Failed to check auth status:', error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  // 登录 - 调用后端API进行验证
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      // 调用登录API
      const response = await fetch('https://api-admin.liubao.site/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      })

      // 检查响应状态
      if (!response.ok) {
        console.error('Login failed with status:', response.status)
        return false
      }

      // 解析响应数据
      const data = await response.json()

      // 验证响应数据格式
      if (!data.user || !data.token) {
        console.error('Invalid response format:', data)
        return false
      }

      // 保存token和用户信息到本地存储
      localStorage.setItem('auth_token', data.token)
      localStorage.setItem('auth_user', JSON.stringify(data.user))

      // 更新状态
      setUser(data.user)
      return true
    } catch (error) {
      console.error('Login failed:', error)
      return false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 清理本地存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
      setUser(null)
      router.push('/login')
    } catch (error) {
      console.error('Logout failed:', error)
      setUser(null)
      router.push('/login')
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!user) return false
    return user.role.permissions.includes(permission) || user.role.permissions.includes('admin')
  }

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const value = {
    user,
    loading,
    login,
    logout,
    hasPermission,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
