import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'
import { userService, sessionService } from './database'
import { isConfigured } from './supabase'

const JWT_SECRET = process.env.JWT_SECRET!
const COOKIE_NAME = 'admin-session'
const SESSION_DURATION = 365 * 24 * 60 * 60 * 1000 // 365天

export interface AuthUser {
  id: string
  username: string
  email?: string
  role: {
    id: string
    name: string
    description?: string
    permissions: string[]
    created_at: string
    updated_at: string
  }
  created_at: string
  updated_at: string
}

// 密码哈希
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10)
}

// 验证密码
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

// 生成JWT token
export function generateToken(payload: jwt.JwtPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '365d' })
}

// 验证JWT token
export function verifyToken(token: string): jwt.JwtPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as jwt.JwtPayload
  } catch {
    return null
  }
}

// 登录
export async function login(username: string, password: string): Promise<{ user: AuthUser; token: string } | null> {
  try {
    // 检查数据库是否配置
    if (!isConfigured) {
      console.warn('数据库未配置，使用演示模式')
      // 演示模式：只允许admin用户登录
      if (username === 'admin' && password === 'qpalzm@123') {
        const demoUser: AuthUser = {
          id: 'demo-admin-id',
          username: 'admin',
          email: '<EMAIL>',
          role: {
            id: 'demo-role-id',
            name: '超级管理员',
            description: '拥有所有权限的管理员角色',
            permissions: ['admin'],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        const token = generateToken({
          userId: demoUser.id,
          username: demoUser.username,
          roleId: demoUser.role.id
        })

        // 设置cookie
        const cookieStore = await cookies()
        const expiresAt = new Date(Date.now() + SESSION_DURATION)
        cookieStore.set(COOKIE_NAME, token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          expires: expiresAt,
          path: '/'
        })

        return { user: demoUser, token }
      }
      return null
    }

    // 查找用户
    const user = await userService.getByUsername(username)
    if (!user || !user.role) {
      return null
    }

    // 验证密码
    const isValid = await verifyPassword(password, user.password_hash)
    if (!isValid) {
      return null
    }

    // 生成token
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      roleId: user.role_id
    }
    const token = generateToken(tokenPayload)

    // 保存会话到数据库
    const expiresAt = new Date(Date.now() + SESSION_DURATION)
    await sessionService.create({
      user_id: user.id,
      token,
      expires_at: expiresAt
    })

    // 设置cookie
    const cookieStore = await cookies()
    cookieStore.set(COOKIE_NAME, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: expiresAt,
      path: '/'
    })

    const authUser: AuthUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: {
        id: user.role.id,
        name: user.role.name,
        description: user.role.description,
        permissions: user.role.permissions,
        created_at: user.role.created_at,
        updated_at: user.role.updated_at
      },
      created_at: user.created_at,
      updated_at: user.updated_at
    }

    return { user: authUser, token }
  } catch (error) {
    console.error('Login error:', error)
    return null
  }
}

// 退出登录
export async function logout(): Promise<void> {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get(COOKIE_NAME)?.value

    if (token) {
      // 从数据库删除会话
      await sessionService.deleteByToken(token)
    }

    // 清除cookie
    cookieStore.delete(COOKIE_NAME)
  } catch (error) {
    console.error('Logout error:', error)
  }
}

// 获取当前用户
export async function getCurrentUser(): Promise<AuthUser | null> {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get(COOKIE_NAME)?.value

    if (!token) {
      return null
    }

    // 验证token
    const payload = verifyToken(token)
    if (!payload) {
      return null
    }

    // 演示模式
    if (!isConfigured) {
      if (payload.username === 'admin') {
        return {
          id: 'demo-admin-id',
          username: 'admin',
          email: '<EMAIL>',
          role: {
            id: 'demo-role-id',
            name: '超级管理员',
            description: '拥有所有权限的管理员角色',
            permissions: ['admin'],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      }
      return null
    }

    // 检查数据库中的会话
    const session = await sessionService.getByToken(token)
    if (!session) {
      return null
    }

    // 获取用户信息
    const user = await userService.getById(payload.userId)
    if (!user || !user.role) {
      return null
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: {
        id: user.role.id,
        name: user.role.name,
        description: user.role.description,
        permissions: user.role.permissions,
        created_at: user.role.created_at,
        updated_at: user.role.updated_at
      },
      created_at: user.created_at,
      updated_at: user.updated_at
    }
  } catch (error) {
    console.error('Get current user error:', error)
    return null
  }
}

// 检查权限
export function hasPermission(user: AuthUser | null, permission: string): boolean {
  if (!user) return false
  return user.role.permissions.includes(permission) || user.role.permissions.includes('admin')
}

// 中间件：要求认证
export async function requireAuth(): Promise<AuthUser> {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('未认证')
  }
  return user
}

// 中间件：要求特定权限
export async function requirePermission(permission: string): Promise<AuthUser> {
  const user = await requireAuth()
  if (!hasPermission(user, permission)) {
    throw new Error('权限不足')
  }
  return user
}
