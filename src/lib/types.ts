// 数据库表类型定义

export interface User {
  id: string
  username: string
  nickname?: string
  email?: string
  password_hash: string
  role_id: string
  last_login_at?: string | null
  last_active_at?: string | null
  created_at: string
  updated_at: string
  role?: Role
}

export interface Role {
  id: string
  name: string
  description?: string
  permissions: string[]
  created_at: string
  updated_at: string
}

export interface Augment {
  id: string
  tenant_url: string
  access_token: string
  portal_url?: string
  email?: string
  auth_session?: string
  user_id: string
  status: 'unused' | 'used'
  price: number
  created_at: string
  updated_at: string
  user?: User
  user_name?: string
  user_nickname?: string
}

export interface AugmentStats {
  total: number
  used_count: number
  unused_count: number
  used_revenue: number
  total_revenue: number
}

export interface Session {
  id: string
  user_id: string
  token: string
  expires_at: string
  created_at: string
}

export interface Email {
  id: string
  email: string
  jwt: string
  user_id: string
  user_name: string
  nickname?: string
  view_count: number
  created_at: string
  updated_at: string
}

export interface Mail {
  id: number
  message_id: string
  source: string
  address: string
  raw: string
  created_at: string
}

export interface ParsedMail {
  subject: string
  from: string
  to: string
  date: string
  htmlContent?: string
  textContent?: string
}

// 权限常量
export const PERMISSIONS = {
  USERS_READ: 'users:read',
  USERS_WRITE: 'users:write',
  USERS_DELETE: 'users:delete',
  ROLES_READ: 'roles:read',
  ROLES_WRITE: 'roles:write',
  ROLES_DELETE: 'roles:delete',
  AUGMENTS_READ: 'augments:read',
  AUGMENTS_WRITE: 'augments:write',
  AUGMENTS_DELETE: 'augments:delete',
  ADMIN: 'admin',
} as const

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]

// API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 分页类型
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}
