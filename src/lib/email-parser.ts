// 邮件MIME格式解析工具
import { ParsedMail } from './types'

// 解析邮件头部信息
function parseHeader(raw: string, headerName: string): string {
  const regex = new RegExp(`${headerName}:\\s*(.+?)(?=\\r?\\n[^\\s]|\\r?\\n\\r?\\n|$)`, 'is')
  const match = raw.match(regex)
  if (match && match[1]) {
    // 处理多行头部（以空格或tab开头的续行）
    return match[1].replace(/\r?\n[\s\t]+/g, ' ').trim()
  }
  return ''
}

// 解码MIME编码的文本（如 =?UTF-8?B?...?= 或 =?UTF-8?Q?...?=）
function decodeMimeEncoded(text: string): string {
  if (!text) return text
  
  // 匹配 =?charset?encoding?encoded-text?= 格式
  const regex = /=\?([^?]+)\?([BQ])\?([^?]+)\?=/gi
  
  return text.replace(regex, (match, charset, encoding, encodedText) => {
    try {
      if (encoding.toUpperCase() === 'B') {
        // Base64解码
        return atob(encodedText)
      } else if (encoding.toUpperCase() === 'Q') {
        // Quoted-Printable解码
        return encodedText
          .replace(/_/g, ' ')
          .replace(/=([0-9A-F]{2})/gi, (_match: string, hex: string) => 
            String.fromCharCode(parseInt(hex, 16))
          )
      }
    } catch (e) {
      console.error('Decode error:', e)
    }
    return match
  })
}

// 提取HTML内容
function extractHtmlContent(raw: string): string | undefined {
  // 查找Content-Type: text/html的部分
  const htmlRegex = /Content-Type:\s*text\/html[^]*?(?=\r?\n\r?\n)([\s\S]*?)(?=--|\r?\n--|\r?\n\r?\nContent-Type:|\r?\n\.\r?\n|$)/i
  const match = raw.match(htmlRegex)
  
  if (match) {
    const htmlContent = match[0]
    
    // 检查是否使用了base64编码
    if (/Content-Transfer-Encoding:\s*base64/i.test(htmlContent)) {
      // 提取base64编码的内容
      const base64Match = htmlContent.match(/\r?\n\r?\n([\s\S]+?)(?=\r?\n--|\r?\n\.\r?\n|$)/)
      if (base64Match) {
        try {
          // 移除换行符
          const base64Content = base64Match[1].replace(/\r?\n/g, '')
          return atob(base64Content)
        } catch (e) {
          console.error('Base64 decode error:', e)
        }
      }
    }
    
    // 检查是否使用了quoted-printable编码
    if (/Content-Transfer-Encoding:\s*quoted-printable/i.test(htmlContent)) {
      const qpMatch = htmlContent.match(/\r?\n\r?\n([\s\S]+?)(?=\r?\n--|\r?\n\.\r?\n|$)/)
      if (qpMatch) {
        return decodeQuotedPrintable(qpMatch[1])
      }
    }
    
    // 如果没有特殊编码，直接提取内容
    const contentMatch = htmlContent.match(/\r?\n\r?\n([\s\S]+?)(?=\r?\n--|\r?\n\.\r?\n|$)/)
    if (contentMatch) {
      return contentMatch[1]
    }
  }
  
  return undefined
}

// 提取纯文本内容
function extractTextContent(raw: string): string | undefined {
  // 查找Content-Type: text/plain的部分
  const textRegex = /Content-Type:\s*text\/plain[^]*?(?=\r?\n\r?\n)([\s\S]*?)(?=--|\r?\n--|\r?\n\r?\nContent-Type:|\r?\n\.\r?\n|$)/i
  const match = raw.match(textRegex)
  
  if (match) {
    const textContent = match[0]
    
    // 检查是否使用了base64编码
    if (/Content-Transfer-Encoding:\s*base64/i.test(textContent)) {
      const base64Match = textContent.match(/\r?\n\r?\n([\s\S]+?)(?=\r?\n--|\r?\n\.\r?\n|$)/)
      if (base64Match) {
        try {
          const base64Content = base64Match[1].replace(/\r?\n/g, '')
          return atob(base64Content)
        } catch (e) {
          console.error('Base64 decode error:', e)
        }
      }
    }
    
    // 检查是否使用了quoted-printable编码
    if (/Content-Transfer-Encoding:\s*quoted-printable/i.test(textContent)) {
      const qpMatch = textContent.match(/\r?\n\r?\n([\s\S]+?)(?=\r?\n--|\r?\n\.\r?\n|$)/)
      if (qpMatch) {
        return decodeQuotedPrintable(qpMatch[1])
      }
    }
    
    // 如果没有特殊编码，直接提取内容
    const contentMatch = textContent.match(/\r?\n\r?\n([\s\S]+?)(?=\r?\n--|\r?\n\.\r?\n|$)/)
    if (contentMatch) {
      return contentMatch[1]
    }
  }
  
  return undefined
}

// Quoted-Printable解码
function decodeQuotedPrintable(text: string): string {
  return text
    .replace(/=\r?\n/g, '') // 移除软换行
    .replace(/=([0-9A-F]{2})/gi, (_match: string, hex: string) => 
      String.fromCharCode(parseInt(hex, 16))
    )
}

// 解析完整的邮件
export function parseMail(raw: string): ParsedMail {
  const subject = decodeMimeEncoded(parseHeader(raw, 'Subject'))
  const from = decodeMimeEncoded(parseHeader(raw, 'From'))
  const to = decodeMimeEncoded(parseHeader(raw, 'To'))
  const date = parseHeader(raw, 'Date')
  
  const htmlContent = extractHtmlContent(raw)
  const textContent = extractTextContent(raw)
  
  return {
    subject: subject || '(无主题)',
    from: from || '(未知发件人)',
    to: to || '',
    date: date || '',
    htmlContent,
    textContent
  }
}

// 格式化邮件日期
export function formatEmailDate(dateString: string): string {
  if (!dateString) return ''
  
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return dateString
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
  } catch {
    return dateString
  }
}

// 生成随机邮箱前缀（atm + 10位随机字符）
export function generateEmailPrefix(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let random = ''
  for (let i = 0; i < 10; i++) {
    random += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return 'atm' + random
}

