import { z } from "zod"

// 登录表单验证
export const loginSchema = z.object({
  username: z.string().min(1, "用户名不能为空"),
  password: z.string().min(1, "密码不能为空"),
})

// 用户表单验证
export const userSchema = z.object({
  username: z.string().min(3, "用户名至少3个字符").max(50, "用户名最多50个字符"),
  nickname: z.string().max(50, "昵称最多50个字符").optional().or(z.literal("")),
  email: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
  password: z.string().min(6, "密码至少6个字符").or(z.literal("")),
  roleId: z.string().min(1, "请选择角色"),
})

// 角色表单验证
export const roleSchema = z.object({
  name: z.string().min(1, "角色名称不能为空").max(50, "角色名称最多50个字符"),
  permissions: z.array(z.string()).min(1, "至少选择一个权限"),
})

// Augment表单验证
export const augmentSchema = z.object({
  tenantUrl: z.string().url("请输入有效的租户URL"),
  accessToken: z.string().min(1, "访问令牌不能为空"),
  portalUrl: z.string().url("请输入有效的Portal URL").optional().or(z.literal("")),
  email: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
  authSession: z.string().optional().or(z.literal("")),
  userId: z.string().min(1, "请选择所属用户"),
  price: z.number().min(0, "价格不能为负数").optional(),
})

export type LoginFormData = z.infer<typeof loginSchema>
export type UserFormData = z.infer<typeof userSchema>
export type RoleFormData = z.infer<typeof roleSchema>
export type AugmentFormData = z.infer<typeof augmentSchema>
