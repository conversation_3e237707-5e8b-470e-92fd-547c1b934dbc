// Cloudflare D1 数据库服务
import { User, Role, Augment, Session, PaginatedResponse } from './types'

// 数据库API基础URL - 使用部署的Cloudflare Worker API
const API_BASE_URL = 'https://api-admin.liubao.site'

// 获取认证token
function getAuthToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token')
  }
  return null
}

// 通用API调用函数
async function apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const token = getAuthToken()
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  }

  // 添加Authorization header
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  // 合并自定义headers
  if (options.headers) {
    Object.assign(headers, options.headers)
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  })

  if (!response.ok) {
    // 尝试解析错误响应体
    try {
      const errorData = await response.json()
      const errorMessage = errorData.error || errorData.message || response.statusText
      throw new Error(errorMessage)
    } catch (parseError) {
      // 如果无法解析JSON，使用状态文本
      throw new Error(`API调用失败: ${response.statusText}`)
    }
  }

  return response.json()
}

// 用户服务
export const d1UserService = {
  async getAll(page = 1, limit = 10): Promise<PaginatedResponse<User>> {
    return apiCall(`/api/users?page=${page}&limit=${limit}`)
  },

  async getById(id: string): Promise<User | null> {
    try {
      return await apiCall(`/api/users/${id}`)
    } catch {
      return null
    }
  },

  async getByUsername(username: string): Promise<User | null> {
    try {
      return await apiCall(`/api/users/by-username/${username}`)
    } catch {
      return null
    }
  },

  async create(userData: {
    username: string
    email: string
    password: string
    role_id: string
  }): Promise<User> {
    return apiCall('/api/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  },

  async update(id: string, userData: {
    username?: string
    email?: string
    password?: string
    role_id?: string
  }): Promise<User> {
    return apiCall(`/api/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    })
  },

  async delete(id: string): Promise<void> {
    await apiCall(`/api/users/${id}`, {
      method: 'DELETE',
    })
  }
}

// 角色服务
export const d1RoleService = {
  async getAll(page = 1, limit = 10): Promise<PaginatedResponse<Role>> {
    return apiCall(`/api/roles?page=${page}&limit=${limit}`)
  },

  async getById(id: string): Promise<Role | null> {
    try {
      return await apiCall(`/api/roles/${id}`)
    } catch {
      return null
    }
  },

  async create(roleData: {
    name: string
    description?: string
    permissions: string[]
  }): Promise<Role> {
    return apiCall('/api/roles', {
      method: 'POST',
      body: JSON.stringify(roleData),
    })
  },

  async update(id: string, roleData: {
    name?: string
    description?: string
    permissions?: string[]
  }): Promise<Role> {
    return apiCall(`/api/roles/${id}`, {
      method: 'PUT',
      body: JSON.stringify(roleData),
    })
  },

  async delete(id: string): Promise<void> {
    await apiCall(`/api/roles/${id}`, {
      method: 'DELETE',
    })
  }
}

// Augment服务
export const d1AugmentService = {
  async getAll(page = 1, limit = 10): Promise<PaginatedResponse<Augment>> {
    return apiCall(`/api/augments?page=${page}&limit=${limit}`)
  },

  async getById(id: string): Promise<Augment | null> {
    try {
      return await apiCall(`/api/augments/${id}`)
    } catch {
      return null
    }
  },

  async create(augmentData: {
    tenant_url: string
    access_token: string
    portal_url?: string
    email?: string
    auth_session?: string
    user_id: string
  }): Promise<Augment> {
    return apiCall('/api/augments', {
      method: 'POST',
      body: JSON.stringify(augmentData),
    })
  },

  async update(id: string, augmentData: {
    tenant_url?: string
    access_token?: string
    portal_url?: string
    email?: string
    auth_session?: string
    user_id?: string
  }): Promise<Augment> {
    return apiCall(`/api/augments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(augmentData),
    })
  },

  async delete(id: string): Promise<void> {
    await apiCall(`/api/augments/${id}`, {
      method: 'DELETE',
    })
  }
}

// 会话服务
export const d1SessionService = {
  async create(sessionData: {
    user_id: string
    token: string
    expires_at: Date
  }): Promise<Session> {
    return apiCall('/api/sessions', {
      method: 'POST',
      body: JSON.stringify({
        ...sessionData,
        expires_at: sessionData.expires_at.toISOString()
      }),
    })
  },

  async getByToken(token: string): Promise<Session | null> {
    try {
      return await apiCall(`/api/sessions/${token}`)
    } catch {
      return null
    }
  },

  async deleteByToken(token: string): Promise<void> {
    await apiCall(`/api/sessions/${token}`, {
      method: 'DELETE',
    })
  },

  async deleteExpired(): Promise<void> {
    await apiCall('/api/sessions/cleanup', {
      method: 'POST',
    })
  }
}
