'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { X, Eye, EyeOff } from 'lucide-react'
import { augmentSchema, type AugmentFormData } from '@/lib/validations'
import { Augment, User } from '@/lib/types'
import { cn } from '@/lib/utils'

interface AugmentFormProps {
  augment?: Augment | null
  users: User[]
  onSuccess: () => void
  onCancel: () => void
}

export function AugmentForm({ augment, users, onSuccess, onCancel }: AugmentFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showToken, setShowToken] = useState(false)
  const isEditing = !!augment

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm<AugmentFormData>({
    resolver: zodResolver(augmentSchema),
    defaultValues: {
      tenantUrl: augment?.tenant_url || '',
      accessToken: augment?.access_token || '',
      portalUrl: augment?.portal_url || '',
      email: augment?.email || '',
      authSession: augment?.auth_session || '',
      userId: augment?.user_id || '',
      price: augment?.price || 0
    }
  })

  const onSubmit = async (data: AugmentFormData) => {
    setIsLoading(true)
    try {
      // 获取token
      const token = localStorage.getItem('auth_token')

      // 直接使用Worker API端点
      const baseUrl = 'https://api-admin.liubao.site'
      const url = isEditing ? `${baseUrl}/api/augments/${augment.id}` : `${baseUrl}/api/augments`
      const method = isEditing ? 'PUT' : 'POST'

      // 转换字段名以匹配API
      const submitData = {
        tenant_url: data.tenantUrl,
        access_token: data.accessToken,
        portal_url: data.portalUrl,
        email: data.email,
        auth_session: data.authSession,
        user_id: data.userId,
        price: data.price || 0
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify(submitData),
      })

      const result = await response.json()

      if (response.ok) {
        onSuccess()
      } else {
        setError('root', {
          message: result.error || '操作失败'
        })
      }
    } catch {
      setError('root', {
        message: '网络错误，请重试'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Transition.Root show={true} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onCancel}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onCancel}
                  >
                    <span className="sr-only">关闭</span>
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isEditing ? '编辑Augment' : '添加Augment'}
                    </Dialog.Title>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-4">
                      {/* 租户URL */}
                      <div>
                        <label htmlFor="tenantUrl" className="block text-sm font-medium text-gray-700">
                          租户URL *
                        </label>
                        <input
                          {...register('tenantUrl')}
                          type="url"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.tenantUrl ? "border-red-300" : ""
                          )}
                          placeholder="https://example.augment.com"
                        />
                        {errors.tenantUrl && (
                          <p className="mt-1 text-sm text-red-600">{errors.tenantUrl.message}</p>
                        )}
                      </div>

                      {/* 访问令牌 */}
                      <div>
                        <label htmlFor="accessToken" className="block text-sm font-medium text-gray-700">
                          访问令牌 *
                        </label>
                        <div className="relative mt-1">
                          <input
                            {...register('accessToken')}
                            type={showToken ? 'text' : 'password'}
                            className={cn(
                              "block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm pr-10",
                              errors.accessToken ? "border-red-300" : ""
                            )}
                            placeholder="请输入访问令牌"
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowToken(!showToken)}
                          >
                            {showToken ? (
                              <EyeOff className="h-4 w-4 text-gray-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-400" />
                            )}
                          </button>
                        </div>
                        {errors.accessToken && (
                          <p className="mt-1 text-sm text-red-600">{errors.accessToken.message}</p>
                        )}
                      </div>

                      {/* Portal URL */}
                      <div>
                        <label htmlFor="portalUrl" className="block text-sm font-medium text-gray-700">
                          Portal URL
                        </label>
                        <input
                          {...register('portalUrl')}
                          type="url"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.portalUrl ? "border-red-300" : ""
                          )}
                          placeholder="https://portal.example.com（可选）"
                        />
                        {errors.portalUrl && (
                          <p className="mt-1 text-sm text-red-600">{errors.portalUrl.message}</p>
                        )}
                      </div>

                      {/* 邮箱 */}
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                          邮箱
                        </label>
                        <input
                          {...register('email')}
                          type="email"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.email ? "border-red-300" : ""
                          )}
                          placeholder="<EMAIL>（可选）"
                        />
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                        )}
                      </div>

                      {/* Auth Session */}
                      <div>
                        <label htmlFor="authSession" className="block text-sm font-medium text-gray-700">
                          Auth Session
                        </label>
                        <textarea
                          {...register('authSession')}
                          rows={3}
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.authSession ? "border-red-300" : ""
                          )}
                          placeholder="认证会话信息（可选）"
                        />
                        {errors.authSession && (
                          <p className="mt-1 text-sm text-red-600">{errors.authSession.message}</p>
                        )}
                      </div>

                      {/* 所属用户 */}
                      <div>
                        <label htmlFor="userId" className="block text-sm font-medium text-gray-700">
                          所属用户 *
                        </label>
                        <select
                          {...register('userId')}
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.userId ? "border-red-300" : ""
                          )}
                        >
                          <option value="">请选择用户</option>
                          {users.map((user) => (
                            <option key={user.id} value={user.id}>
                              {user.nickname || user.username} {user.email && `(${user.email})`}
                            </option>
                          ))}
                        </select>
                        {errors.userId && (
                          <p className="mt-1 text-sm text-red-600">{errors.userId.message}</p>
                        )}
                      </div>

                      {/* 售卖价格 */}
                      <div>
                        <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                          售卖价格 (¥)
                        </label>
                        <input
                          {...register('price', { valueAsNumber: true })}
                          type="number"
                          step="0.01"
                          min="0"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.price ? "border-red-300" : ""
                          )}
                          placeholder="0.00"
                        />
                        {errors.price && (
                          <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
                        )}
                      </div>

                      {/* 错误信息 */}
                      {errors.root && (
                        <div className="rounded-md bg-red-50 p-4">
                          <p className="text-sm text-red-800">{errors.root.message}</p>
                        </div>
                      )}

                      {/* 按钮 */}
                      <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                        <button
                          type="submit"
                          disabled={isLoading}
                          className={cn(
                            "inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2",
                            isLoading
                              ? "bg-gray-400 cursor-not-allowed"
                              : "bg-blue-600 hover:bg-blue-500 focus-visible:outline-blue-600"
                          )}
                        >
                          {isLoading ? '保存中...' : (isEditing ? '更新' : '创建')}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                          onClick={onCancel}
                        >
                          取消
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
