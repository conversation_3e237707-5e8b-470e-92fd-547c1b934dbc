'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { X, Eye, EyeOff } from 'lucide-react'
import { userSchema, type UserFormData } from '@/lib/validations'
import { User, Role } from '@/lib/types'
import { cn } from '@/lib/utils'

interface UserFormProps {
  user?: User | null
  roles: Role[]
  onSuccess: () => void
  onCancel: () => void
}

export function UserForm({ user, roles, onSuccess, onCancel }: UserFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const isEditing = !!user

  // 查找"普通用户"角色作为默认值
  const defaultRole = roles.find(role => role.name === '普通用户')
  const defaultRoleId = defaultRole?.id || ''

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      username: user?.username || '',
      nickname: user?.nickname || '',
      email: user?.email || '',
      roleId: user?.role_id || defaultRoleId,
      password: ''
    }
  })

  const onSubmit = async (data: UserFormData) => {
    setIsLoading(true)
    try {
      // 直接使用Worker API端点
      const baseUrl = 'https://api-admin.liubao.site'
      const url = isEditing ? `${baseUrl}/api/users/${user.id}` : `${baseUrl}/api/users`
      const method = isEditing ? 'PUT' : 'POST'

      // 如果是编辑且没有输入新密码，不发送密码字段
      const submitData: Partial<UserFormData> = { ...data }
      if (isEditing && !data.password) {
        delete submitData.password
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      const result = await response.json()

      if (response.ok) {
        onSuccess()
      } else {
        setError('root', {
          message: result.error || '操作失败'
        })
      }
    } catch {
      setError('root', {
        message: '网络错误，请重试'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Transition.Root show={true} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onCancel}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onCancel}
                  >
                    <span className="sr-only">关闭</span>
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isEditing ? '编辑用户' : '添加用户'}
                    </Dialog.Title>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-4">
                      {/* 用户名 */}
                      <div>
                        <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                          用户名 *
                        </label>
                        <input
                          {...register('username')}
                          type="text"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.username ? "border-red-300" : ""
                          )}
                          placeholder="请输入用户名"
                        />
                        {errors.username && (
                          <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
                        )}
                      </div>

                      {/* 昵称 */}
                      <div>
                        <label htmlFor="nickname" className="block text-sm font-medium text-gray-700">
                          昵称
                        </label>
                        <input
                          {...register('nickname')}
                          type="text"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.nickname ? "border-red-300" : ""
                          )}
                          placeholder="请输入昵称（可选）"
                        />
                        {errors.nickname && (
                          <p className="mt-1 text-sm text-red-600">{errors.nickname.message}</p>
                        )}
                      </div>

                      {/* 邮箱 */}
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                          邮箱
                        </label>
                        <input
                          {...register('email')}
                          type="email"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.email ? "border-red-300" : ""
                          )}
                          placeholder="请输入邮箱地址"
                        />
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                        )}
                      </div>

                      {/* 密码 */}
                      <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                          密码 {!isEditing && '*'}
                        </label>
                        <div className="relative mt-1">
                          <input
                            {...register('password')}
                            type={showPassword ? 'text' : 'password'}
                            className={cn(
                              "block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm pr-10",
                              errors.password ? "border-red-300" : ""
                            )}
                            placeholder={isEditing ? "留空表示不修改密码" : "请输入密码"}
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-400" />
                            )}
                          </button>
                        </div>
                        {errors.password && (
                          <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                        )}
                      </div>

                      {/* 角色 */}
                      <div>
                        <label htmlFor="roleId" className="block text-sm font-medium text-gray-700">
                          角色 *
                        </label>
                        <select
                          {...register('roleId')}
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.roleId ? "border-red-300" : ""
                          )}
                        >
                          <option value="">请选择角色</option>
                          {roles.map((role) => (
                            <option key={role.id} value={role.id}>
                              {role.name}
                            </option>
                          ))}
                        </select>
                        {errors.roleId && (
                          <p className="mt-1 text-sm text-red-600">{errors.roleId.message}</p>
                        )}
                      </div>

                      {/* 错误信息 */}
                      {errors.root && (
                        <div className="rounded-md bg-red-50 p-4">
                          <p className="text-sm text-red-800">{errors.root.message}</p>
                        </div>
                      )}

                      {/* 按钮 */}
                      <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                        <button
                          type="submit"
                          disabled={isLoading}
                          className={cn(
                            "inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2",
                            isLoading
                              ? "bg-gray-400 cursor-not-allowed"
                              : "bg-blue-600 hover:bg-blue-500 focus-visible:outline-blue-600"
                          )}
                        >
                          {isLoading ? '保存中...' : (isEditing ? '更新' : '创建')}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                          onClick={onCancel}
                        >
                          取消
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
