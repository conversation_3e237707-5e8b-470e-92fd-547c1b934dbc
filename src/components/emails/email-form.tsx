'use client'

import { useState, useEffect, Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { X, Mail, RefreshCw } from 'lucide-react'
import { generateEmailPrefix } from '@/lib/email-parser'
import { cn } from '@/lib/utils'

interface EmailFormProps {
  onSuccess: () => void
  onCancel: () => void
}

const DOMAINS = [
  'niubea.fun',
  'niubea.online',
  'niubea.site',
  'nbai.asia'
]

export function EmailForm({ onSuccess, onCancel }: EmailFormProps) {
  const [emailPrefix, setEmailPrefix] = useState('')
  const [selectedDomain, setSelectedDomain] = useState(DOMAINS[0])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [dailyCount, setDailyCount] = useState(0)
  const [isLoadingCount, setIsLoadingCount] = useState(true)

  // 生成随机邮箱前缀
  const generatePrefix = () => {
    const prefix = generateEmailPrefix()
    setEmailPrefix(prefix)
  }

  // 获取今日创建数量
  const fetchDailyCount = async () => {
    try {
      setIsLoadingCount(true)
      const response = await fetch('https://api-admin.liubao.site/api/emails/daily-count', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setDailyCount(data.count || 0)
      }
    } catch (err) {
      console.error('Fetch daily count error:', err)
    } finally {
      setIsLoadingCount(false)
    }
  }

  useEffect(() => {
    generatePrefix()
    fetchDailyCount()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!emailPrefix) {
      setError('请先生成邮箱地址')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const name = emailPrefix
      const domain = selectedDomain

      const response = await fetch('https://api-admin.liubao.site/api/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name, domain })
      })

      const result = await response.json()

      if (response.ok) {
        onSuccess()
      } else {
        setError(result.error || '创建邮箱失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
      console.error('Create email error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const fullEmail = emailPrefix ? `${emailPrefix}@${selectedDomain}` : ''
  const canCreate = !isLoadingCount && dailyCount < 3

  return (
    <Transition.Root show={true} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onCancel}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onCancel}
                  >
                    <span className="sr-only">关闭</span>
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                    <Mail className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      创建临时邮箱
                    </Dialog.Title>

                    <form onSubmit={handleSubmit} className="mt-6 space-y-4">
                      {/* 今日创建数量提示 */}
                      {!isLoadingCount && (
                        <div className={cn(
                          "rounded-md p-3 text-sm",
                          dailyCount >= 3 ? "bg-red-50 text-red-800" : "bg-blue-50 text-blue-800"
                        )}>
                          今日已创建 {dailyCount}/3 个邮箱
                          {dailyCount >= 3 && " (已达上限)"}
                        </div>
                      )}

                      {/* 邮箱预览 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          邮箱地址预览
                        </label>
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900 font-mono text-sm">
                            {fullEmail || '点击生成邮箱地址'}
                          </div>
                          <button
                            type="button"
                            onClick={generatePrefix}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            title="重新生成"
                          >
                            <RefreshCw className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      {/* 域名选择 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          选择域名后缀
                        </label>
                        <div className="grid grid-cols-2 gap-2">
                          {DOMAINS.map((domain) => (
                            <button
                              key={domain}
                              type="button"
                              onClick={() => setSelectedDomain(domain)}
                              className={cn(
                                "px-4 py-2 text-sm font-medium rounded-md border transition-colors",
                                selectedDomain === domain
                                  ? "bg-blue-600 text-white border-blue-600"
                                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                              )}
                            >
                              @{domain}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* 错误信息 */}
                      {error && (
                        <div className="rounded-md bg-red-50 p-4">
                          <p className="text-sm text-red-800">{error}</p>
                        </div>
                      )}

                      {/* 按钮 */}
                      <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                        <button
                          type="submit"
                          disabled={isLoading || !canCreate}
                          className={cn(
                            "inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2",
                            isLoading || !canCreate
                              ? "bg-gray-400 cursor-not-allowed"
                              : "bg-blue-600 hover:bg-blue-500 focus-visible:outline-blue-600"
                          )}
                        >
                          {isLoading ? '创建中...' : '保存邮箱'}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                          onClick={onCancel}
                        >
                          取消
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}

