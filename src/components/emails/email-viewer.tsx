'use client'

import { useState, useEffect, Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { X, Mail, RefreshCw, Download, FileText } from 'lucide-react'
import { Email, Mail as MailType, ParsedMail } from '@/lib/types'
import { parseMail, formatEmailDate, extractMailHeaders, parseMailAsync } from '@/lib/email-parser'
import DOMPurify from 'dompurify'

interface EmailViewerProps {
  email: Email
  onClose: () => void
}

export function EmailViewer({ email, onClose }: EmailViewerProps) {
  const [mails, setMails] = useState<MailType[]>([])
  const [selectedMail, setSelectedMail] = useState<MailType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)

  // 邮件解析相关状态
  const [parsedMail, setParsedMail] = useState<ParsedMail | null>(null)
  const [parsedMailLoading, setParsedMailLoading] = useState(false)
  const [parsedMailError, setParsedMailError] = useState('')
  const [parsedMailCache, setParsedMailCache] = useState<Map<number, ParsedMail>>(new Map())

  // 获取邮件列表
  const fetchMails = async (pageNum = 1) => {
    try {
      setLoading(true)
      setError('')
      
      const limit = 20
      const offset = (pageNum - 1) * limit

      // 使用后端代理接口而不是直接调用第三方API
      const response = await fetch(
        `https://api-admin.liubao.site/api/emails/${email.id}/mails?limit=${limit}&offset=${offset}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            'Content-Type': 'application/json'
          }
        }
      )

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API error response:', errorText)
        throw new Error(`获取邮件失败: ${response.status}`)
      }

      const data = await response.json()
      
      // 确保数据格式正确
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format')
      }
      
      const newMails = Array.isArray(data.results) ? data.results : []
      
      setMails(prev => pageNum === 1 ? newMails : [...prev, ...newMails])
      setHasMore(newMails.length === limit)
      
      // 默认选中第一封邮件
      if (pageNum === 1 && newMails.length > 0 && !selectedMail) {
        setSelectedMail(newMails[0])
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取邮件列表失败'
      setError(errorMessage)
      console.error('Fetch mails error:', err)
    } finally {
      setLoading(false)
    }
  }

  // 增加查看次数
  const incrementViewCount = async () => {
    try {
      await fetch(`https://api-admin.liubao.site/api/emails/${email.id}/view`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      })
    } catch (err) {
      console.error('Update view count error:', err)
    }
  }

  // 异步解析选中的邮件
  useEffect(() => {
    if (!selectedMail) {
      setParsedMail(null)
      setParsedMailError('')
      return
    }

    // 检查缓存
    if (parsedMailCache.has(selectedMail.id)) {
      setParsedMail(parsedMailCache.get(selectedMail.id) || null)
      return
    }

    // 异步解析邮件
    const parseEmail = async () => {
      try {
        setParsedMailLoading(true)
        setParsedMailError('')

        // 尝试使用 postal-mime 解析
        const parsed = await parseMailAsync(selectedMail.raw)
        setParsedMail(parsed)

        // 更新缓存
        const newCache = new Map(parsedMailCache)
        newCache.set(selectedMail.id, parsed)
        setParsedMailCache(newCache)
      } catch (err) {
        console.warn('Async parsing failed, falling back to sync parsing:', err)
        // 降级到同步解析
        try {
          const parsed = parseMail(selectedMail.raw)
          setParsedMail(parsed)

          // 更新缓存
          const newCache = new Map(parsedMailCache)
          newCache.set(selectedMail.id, parsed)
          setParsedMailCache(newCache)
        } catch (syncErr) {
          console.error('Sync parsing also failed:', syncErr)
          setParsedMailError('邮件解析失败')
        }
      } finally {
        setParsedMailLoading(false)
      }
    }

    parseEmail()
  }, [selectedMail, parsedMailCache])

  useEffect(() => {
    fetchMails()
    incrementViewCount()
  }, [])

  // 加载更多
  const loadMore = () => {
    const nextPage = page + 1
    setPage(nextPage)
    fetchMails(nextPage)
  }

  // 刷新邮件列表
  const refresh = () => {
    setPage(1)
    setSelectedMail(null)
    fetchMails(1)
  }

  // 渲染邮件内容
  const renderMailContent = () => {
    if (!selectedMail) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <Mail className="h-16 w-16" />
          <p className="ml-4">选择一封邮件查看内容</p>
        </div>
      )
    }

    // 如果正在加载，显示加载状态
    if (parsedMailLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">解析邮件中...</p>
          </div>
        </div>
      )
    }

    // 如果解析失败，显示错误信息
    if (parsedMailError) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-red-600">
            <Mail className="h-12 w-12 mx-auto mb-2" />
            <p>{parsedMailError}</p>
          </div>
        </div>
      )
    }

    // 如果没有解析结果，使用降级方案
    const mailData = parsedMail || parseMail(selectedMail.raw)
    const mailHeaders = extractMailHeaders(selectedMail.raw)

    return (
      <div className="h-full overflow-auto flex flex-col">
        {/* 邮件头信息 */}
        <div className="border-b border-gray-200 bg-gray-50 px-4 py-4 space-y-2 flex-shrink-0">
          <h3 className="text-lg font-semibold text-gray-900">{mailHeaders.subject || '(无主题)'}</h3>
          <div className="text-sm text-gray-600 space-y-1 font-mono text-xs">
            <div><span className="font-medium text-gray-700">FROM:</span> {mailHeaders.from || '未知'}</div>
            <div><span className="font-medium text-gray-700">TO:</span> {selectedMail.address || mailHeaders.to || '未知'}</div>
            <div><span className="font-medium text-gray-700">DATE:</span> {formatEmailDate(selectedMail.created_at)}</div>
          </div>
        </div>

        {/* 邮件内容 */}
        <div className="flex-1 overflow-auto p-4">
          <style>{`
            .html-content-wrapper {
              all: revert;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
              font-size: 14px;
              line-height: 1.5;
              color: #333;
              max-width: 100%;
            }
            .html-content-wrapper * {
              box-sizing: border-box;
              max-width: 100%;
            }
            .html-content-wrapper table {
              border-collapse: collapse;
              width: 100%;
            }
            .html-content-wrapper td, .html-content-wrapper th {
              padding: 8px;
              border: 1px solid #ddd;
            }
            .html-content-wrapper img {
              max-width: 100%;
              height: auto;
              display: block;
            }
            .html-content-wrapper a {
              color: #0066cc;
              text-decoration: underline;
            }
            .html-content-wrapper a:hover {
              color: #0052a3;
            }
          `}</style>
          {(() => {
            // 清理 HTML 内容以防止 XSS
            const sanitizedHtml = mailData.htmlContent
              ? DOMPurify.sanitize(mailData.htmlContent, {
                  ALLOWED_TAGS: [
                    'b', 'i', 'em', 'strong', 'a', 'p', 'br', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                    'ul', 'ol', 'li', 'table', 'thead', 'tbody', 'tr', 'td', 'th', 'img', 'blockquote', 'pre',
                    'code', 'hr', 'font', 'center', 'u', 's', 'strike', 'sub', 'sup'
                  ],
                  ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'width', 'height', 'style', 'class', 'id', 'target', 'rel']
                })
              : undefined

            return sanitizedHtml ? (
              <div
                dangerouslySetInnerHTML={{
                  __html: sanitizedHtml
                }}
                className="html-content-wrapper"
                style={{
                  wordWrap: 'break-word',
                  overflow: 'visible',
                  display: 'block'
                }}
              />
            ) : mailData.textContent ? (
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <pre
                  className="whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed break-words max-w-full"
                  style={{
                    wordBreak: 'break-word',
                    overflow: 'visible'
                  }}
                >
                  {mailData.textContent}
                </pre>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                <Mail className="h-12 w-12 mb-3" />
                <p>无法解析邮件内容</p>
                <p className="text-xs mt-2">可能是邮件格式不支持或内容已损坏</p>
              </div>
            )
          })()}

          {/* 附件列表 */}
          {mailData.attachments && mailData.attachments.length > 0 && (
            <div className="mt-6 pt-4 border-t border-gray-200">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">附件 ({mailData.attachments.length})</h4>
              <div className="space-y-2">
                {mailData.attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <FileText className="h-4 w-4 text-gray-400 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{attachment.filename}</p>
                        <p className="text-xs text-gray-500">{attachment.contentType} • {(attachment.size / 1024).toFixed(2)} KB</p>
                      </div>
                    </div>
                    <button
                      className="ml-2 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded flex-shrink-0"
                      title="下载附件"
                    >
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <Transition.Root show={true} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-hidden">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all w-full max-w-7xl h-[80vh]">
                {/* 头部 */}
                <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
                  <div>
                    <Dialog.Title className="text-lg font-semibold text-gray-900">
                      邮件列表 - {email.email}
                    </Dialog.Title>
                    <p className="text-sm text-gray-500 mt-1">
                      共 {mails.length} 封邮件
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={refresh}
                      className="rounded-md p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                      title="刷新"
                    >
                      <RefreshCw className="h-5 w-5" />
                    </button>
                    <button
                      onClick={onClose}
                      className="rounded-md p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* 左右分栏 */}
                <div className="flex h-[calc(100%-80px)]">
                  {/* 左侧邮件列表 */}
                  <div className="w-1/3 border-r border-gray-200 overflow-auto">
                    {loading && mails.length === 0 ? (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                          <p className="mt-2 text-gray-500">加载中...</p>
                        </div>
                      </div>
                    ) : error ? (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center text-red-600">
                          <Mail className="h-12 w-12 mx-auto mb-2" />
                          <p>{error}</p>
                        </div>
                      </div>
                    ) : mails.length === 0 ? (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center text-gray-500">
                          <Mail className="h-12 w-12 mx-auto mb-2" />
                          <p>暂无邮件</p>
                        </div>
                      </div>
                    ) : (
                      <div>
                        {mails.map((mail) => {
                          const mailHeaders = extractMailHeaders(mail.raw)
                          const isSelected = selectedMail?.id === mail.id

                          return (
                            <div
                              key={mail.id}
                              onClick={() => setSelectedMail(mail)}
                              className={`cursor-pointer border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors ${
                                isSelected ? 'bg-blue-50' : ''
                              }`}
                            >
                              <div className="flex items-start space-x-3">
                                <Mail className={`h-4 w-4 mt-0.5 flex-shrink-0 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`} />
                                <div className="flex-1 min-w-0">
                                  {/* 邮件主题 */}
                                  <p className={`text-sm font-semibold truncate ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                                    {mailHeaders.subject || '(无主题)'}
                                  </p>
                                  
                                  {/* 发件人信息 */}
                                  <p className="text-xs text-gray-600 truncate mt-1">
                                    FROM: {mailHeaders.from || '未知'}
                                  </p>
                                  
                                  {/* 收件人信息 */}
                                  <p className="text-xs text-gray-600 truncate">
                                    TO: {mail.address || mailHeaders.to || '未知'}
                                  </p>
                                  
                                  {/* 接收时间 */}
                                  <p className={`text-xs mt-1 ${isSelected ? 'text-blue-500' : 'text-gray-500'}`}>
                                    {formatEmailDate(mail.created_at)}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )
                        })}

                        {/* 加载更多按钮 */}
                        {hasMore && (
                          <div className="p-4 text-center">
                            <button
                              onClick={loadMore}
                              disabled={loading}
                              className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400"
                            >
                              {loading ? '加载中...' : '加载更多'}
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 右侧邮件详情 */}
                  <div className="flex-1 bg-white">
                    {renderMailContent()}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}

