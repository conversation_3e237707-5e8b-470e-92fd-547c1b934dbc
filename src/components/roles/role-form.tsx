'use client'

import { useState } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { X } from 'lucide-react'
import { roleSchema, type RoleFormData } from '@/lib/validations'
import { Role, PERMISSIONS } from '@/lib/types'
import { cn } from '@/lib/utils'

interface RoleFormProps {
  role?: Role | null
  onSuccess: () => void
  onCancel: () => void
}

// 权限分组
const permissionGroups = [
  {
    name: '系统管理',
    permissions: [
      { key: PERMISSIONS.ADMIN, label: '超级管理员', description: '拥有所有权限' }
    ]
  },
  {
    name: '用户管理',
    permissions: [
      { key: PERMISSIONS.USERS_READ, label: '查看用户', description: '查看用户列表和详情' },
      { key: PERMISSIONS.USERS_WRITE, label: '编辑用户', description: '创建和修改用户' },
      { key: PERMISSIONS.USERS_DELETE, label: '删除用户', description: '删除用户账户' }
    ]
  },
  {
    name: '角色管理',
    permissions: [
      { key: PERMISSIONS.ROLES_READ, label: '查看角色', description: '查看角色列表和详情' },
      { key: PERMISSIONS.ROLES_WRITE, label: '编辑角色', description: '创建和修改角色' },
      { key: PERMISSIONS.ROLES_DELETE, label: '删除角色', description: '删除自定义角色' }
    ]
  },
  {
    name: 'Augment管理',
    permissions: [
      { key: PERMISSIONS.AUGMENTS_READ, label: '查看Augment', description: '查看Augment列表和详情' },
      { key: PERMISSIONS.AUGMENTS_WRITE, label: '编辑Augment', description: '创建和修改Augment' },
      { key: PERMISSIONS.AUGMENTS_DELETE, label: '删除Augment', description: '删除Augment配置' }
    ]
  }
]

export function RoleForm({ role, onSuccess, onCancel }: RoleFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const isEditing = !!role

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
    setError
  } = useForm<RoleFormData>({
    resolver: zodResolver(roleSchema),
    defaultValues: {
      name: role?.name || '',
      permissions: role?.permissions || []
    }
  })

  const watchedPermissions = watch('permissions')

  const onSubmit = async (data: RoleFormData) => {
    setIsLoading(true)
    try {
      const API_BASE_URL = 'https://api-admin.liubao.site'
      const url = isEditing ? `${API_BASE_URL}/api/roles/${role.id}` : `${API_BASE_URL}/api/roles`
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (response.ok) {
        onSuccess()
      } else {
        setError('root', {
          message: result.error || '操作失败'
        })
      }
    } catch {
      setError('root', {
        message: '网络错误，请重试'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Transition.Root show={true} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onCancel}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onCancel}
                  >
                    <span className="sr-only">关闭</span>
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      {isEditing ? '编辑角色' : '添加角色'}
                    </Dialog.Title>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-6">
                      {/* 角色名称 */}
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                          角色名称 *
                        </label>
                        <input
                          {...register('name')}
                          type="text"
                          className={cn(
                            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm",
                            errors.name ? "border-red-300" : ""
                          )}
                          placeholder="请输入角色名称"
                        />
                        {errors.name && (
                          <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                        )}
                      </div>

                      {/* 权限选择 */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          权限配置 *
                        </label>
                        <Controller
                          name="permissions"
                          control={control}
                          render={({ field }) => (
                            <div className="space-y-6">
                              {permissionGroups.map((group) => (
                                <div key={group.name} className="border border-gray-200 rounded-lg p-4">
                                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                                    {group.name}
                                  </h4>
                                  <div className="space-y-3">
                                    {group.permissions.map((permission) => (
                                      <div key={permission.key} className="flex items-start">
                                        <div className="flex items-center h-5">
                                          <input
                                            type="checkbox"
                                            checked={field.value.includes(permission.key)}
                                            onChange={(e) => {
                                              const newPermissions = e.target.checked
                                                ? [...field.value, permission.key]
                                                : field.value.filter(p => p !== permission.key)
                                              field.onChange(newPermissions)
                                            }}
                                            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                                          />
                                        </div>
                                        <div className="ml-3 text-sm">
                                          <label className="font-medium text-gray-700">
                                            {permission.label}
                                          </label>
                                          <p className="text-gray-500">{permission.description}</p>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        />
                        {errors.permissions && (
                          <p className="mt-1 text-sm text-red-600">{errors.permissions.message}</p>
                        )}
                      </div>

                      {/* 已选权限预览 */}
                      {watchedPermissions.length > 0 && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            已选权限 ({watchedPermissions.length})
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {watchedPermissions.map((permission) => {
                              const permissionInfo = permissionGroups
                                .flatMap((g: { permissions: { key: string; label: string; description: string }[] }) => g.permissions)
                                .find((p: { key: string; label: string; description: string }) => p.key === permission)
                              return (
                                <span
                                  key={permission}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                >
                                  {permissionInfo?.label || permission}
                                </span>
                              )
                            })}
                          </div>
                        </div>
                      )}

                      {/* 错误信息 */}
                      {errors.root && (
                        <div className="rounded-md bg-red-50 p-4">
                          <p className="text-sm text-red-800">{errors.root.message}</p>
                        </div>
                      )}

                      {/* 按钮 */}
                      <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                        <button
                          type="submit"
                          disabled={isLoading}
                          className={cn(
                            "inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2",
                            isLoading
                              ? "bg-gray-400 cursor-not-allowed"
                              : "bg-blue-600 hover:bg-blue-500 focus-visible:outline-blue-600"
                          )}
                        >
                          {isLoading ? '保存中...' : (isEditing ? '更新' : '创建')}
                        </button>
                        <button
                          type="button"
                          className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                          onClick={onCancel}
                        >
                          取消
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
