'use client'

import { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/layout/admin-layout'
import { useAuth } from '@/lib/auth-context'
import { Email, PaginatedResponse } from '@/lib/types'
import { Plus, Mail, Trash2, Eye } from 'lucide-react'
import { EmailForm } from '@/components/emails/email-form'
import { EmailViewer } from '@/components/emails/email-viewer'
import { formatDate } from '@/lib/utils'
import { useToast } from '@/components/ui/toast'
import { ConfirmDialog } from '@/components/ui/confirm-dialog'

export default function EmailsPage() {
  const { user } = useAuth()
  const { success, error: showError } = useToast()
  const [emails, setEmails] = useState<PaginatedResponse<Email>>({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  })
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [showViewer, setShowViewer] = useState(false)
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null)
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [emailToDelete, setEmailToDelete] = useState<Email | null>(null)
  const [showBatchDeleteDialog, setShowBatchDeleteDialog] = useState(false)

  // 获取认证headers
  const getAuthHeaders = () => ({
    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    'Content-Type': 'application/json'
  })

  // 获取邮箱列表
  const fetchEmails = async (page = 1) => {
    try {
      setLoading(true)
      const response = await fetch(
        `https://api-admin.liubao.site/api/emails?page=${page}&limit=10`,
        {
          headers: getAuthHeaders()
        }
      )
      const result = await response.json()
      setEmails(result)
    } catch (error) {
      console.error('Failed to fetch emails:', error)
      showError('获取失败', '获取邮箱列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchEmails()
  }, [])

  // 查看邮件
  const handleViewMails = (email: Email) => {
    setSelectedEmail(email)
    setShowViewer(true)
  }

  // 删除邮箱
  const handleDelete = (email: Email) => {
    setEmailToDelete(email)
    setShowDeleteDialog(true)
  }

  // 确认删除
  const confirmDelete = async () => {
    if (!emailToDelete) return

    try {
      const response = await fetch(
        `https://api-admin.liubao.site/api/emails/${emailToDelete.id}`,
        {
          method: 'DELETE',
          headers: getAuthHeaders()
        }
      )

      if (response.ok) {
        success('删除成功', `邮箱 "${emailToDelete.email}" 已删除`)
        fetchEmails(emails.page)
        setSelectedIds(prev => prev.filter(id => id !== emailToDelete.id))
      } else {
        const result = await response.json()
        showError('删除失败', result.error || '删除邮箱失败')
      }
    } catch (err) {
      console.error('Delete email error:', err)
      showError('删除失败', '删除邮箱失败，请重试')
    } finally {
      setEmailToDelete(null)
      setShowDeleteDialog(false)
    }
  }

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedIds.length === 0) {
      showError('删除失败', '请先选择要删除的邮箱')
      return
    }
    setShowBatchDeleteDialog(true)
  }

  // 确认批量删除
  const confirmBatchDelete = async () => {
    try {
      const response = await fetch(
        'https://api-admin.liubao.site/api/emails/batch',
        {
          method: 'DELETE',
          headers: getAuthHeaders(),
          body: JSON.stringify({ ids: selectedIds })
        }
      )

      if (response.ok) {
        const result = await response.json()
        success('删除成功', `已删除 ${result.deletedCount} 个邮箱`)
        fetchEmails(emails.page)
        setSelectedIds([])
      } else {
        const result = await response.json()
        showError('删除失败', result.error || '批量删除失败')
      }
    } catch (err) {
      console.error('Batch delete error:', err)
      showError('删除失败', '批量删除失败，请重试')
    } finally {
      setShowBatchDeleteDialog(false)
    }
  }

  // 表单提交成功
  const handleFormSuccess = () => {
    setShowForm(false)
    fetchEmails(emails.page)
    success('创建成功', '邮箱创建成功')
  }

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedIds.length === emails.data.length) {
      setSelectedIds([])
    } else {
      setSelectedIds(emails.data.map(e => e.id))
    }
  }

  // 单选
  const handleSelect = (id: string) => {
    setSelectedIds(prev =>
      prev.includes(id)
        ? prev.filter(i => i !== id)
        : [...prev, id]
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">邮箱管理</h1>
            <p className="mt-1 text-sm text-gray-600">
              管理临时邮箱地址
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            {selectedIds.length > 0 && (
              <button
                onClick={handleBatchDelete}
                className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                批量删除 ({selectedIds.length})
              </button>
            )}
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              创建邮箱
            </button>
          </div>
        </div>

        {/* 邮箱列表 */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">加载中...</p>
            </div>
          ) : emails.data.length === 0 ? (
            <div className="p-6 text-center">
              <Mail className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">暂无邮箱数据</p>
              <button
                onClick={() => setShowForm(true)}
                className="mt-4 text-blue-600 hover:text-blue-800 text-sm"
              >
                创建第一个邮箱
              </button>
            </div>
          ) : (
            <>
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="w-12 px-6 py-3">
                      <input
                        type="checkbox"
                        checked={selectedIds.length === emails.data.length}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      邮箱地址
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建人
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      查看次数
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {emails.data.map((email) => (
                    <tr key={email.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedIds.includes(email.id)}
                          onChange={() => handleSelect(email.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Mail className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="text-sm font-mono text-gray-900">
                            {email.email}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {email.nickname || email.user_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {email.view_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(email.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleViewMails(email)}
                          className="text-blue-600 hover:text-blue-900 mr-4"
                          title="查看邮件"
                        >
                          <Eye className="h-4 w-4 inline" />
                        </button>
                        <button
                          onClick={() => handleDelete(email)}
                          className="text-red-600 hover:text-red-900"
                          title="删除"
                        >
                          <Trash2 className="h-4 w-4 inline" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* 分页 */}
              {emails.totalPages > 1 && (
                <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={() => fetchEmails(emails.page - 1)}
                      disabled={emails.page <= 1}
                      className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      onClick={() => fetchEmails(emails.page + 1)}
                      disabled={emails.page >= emails.totalPages}
                      className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        显示 {(emails.page - 1) * emails.limit + 1} 到{' '}
                        {Math.min(emails.page * emails.limit, emails.total)} 条，共 {emails.total} 条
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <button
                          onClick={() => fetchEmails(emails.page - 1)}
                          disabled={emails.page <= 1}
                          className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                        >
                          上一页
                        </button>
                        <button
                          onClick={() => fetchEmails(emails.page + 1)}
                          disabled={emails.page >= emails.totalPages}
                          className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                        >
                          下一页
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 创建邮箱表单 */}
      {showForm && (
        <EmailForm
          onSuccess={handleFormSuccess}
          onCancel={() => setShowForm(false)}
        />
      )}

      {/* 邮件查看器 */}
      {showViewer && selectedEmail && (
        <EmailViewer
          email={selectedEmail}
          onClose={() => {
            setShowViewer(false)
            setSelectedEmail(null)
            // 重新加载列表以更新查看次数
            fetchEmails(emails.page)
          }}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setEmailToDelete(null)
        }}
        onConfirm={confirmDelete}
        title="删除邮箱"
        message={`确定要删除邮箱 "${emailToDelete?.email}" 吗？此操作无法撤销。`}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />

      {/* 批量删除确认对话框 */}
      <ConfirmDialog
        isOpen={showBatchDeleteDialog}
        onClose={() => setShowBatchDeleteDialog(false)}
        onConfirm={confirmBatchDelete}
        title="批量删除邮箱"
        message={`确定要删除选中的 ${selectedIds.length} 个邮箱吗？此操作无法撤销。`}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />
    </AdminLayout>
  )
}

