'use client'

import { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/layout/admin-layout'
import { useAuth } from '@/lib/auth-context'
import { User, Role, PaginatedResponse } from '@/lib/types'
import { Plus, Edit, Trash2, Search } from 'lucide-react'
import { UserForm } from '@/components/users/user-form'
import { formatDate, formatRelativeTime, formatFullTime } from '@/lib/utils'
import { userService, roleService } from '@/lib/database'
import { useToast } from '@/components/ui/toast'
import { ConfirmDialog } from '@/components/ui/confirm-dialog'

export default function UsersPage() {
  const { hasPermission } = useAuth()
  const { success, error } = useToast()
  const [users, setUsers] = useState<PaginatedResponse<User>>({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  })
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [userToDelete, setUserToDelete] = useState<User | null>(null)

  // 获取用户列表
  const fetchUsers = async (page = 1) => {
    try {
      const result = await userService.getAll(page, 10)
      setUsers(result)
    } catch (error) {
      console.error('Failed to fetch users:', error)
    }
  }

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const result = await roleService.getAll()
      setRoles(result.data)
    } catch (error) {
      console.error('Failed to fetch roles:', error)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchUsers(), fetchRoles()])
      setLoading(false)
    }
    loadData()
  }, [])

  // 删除用户
  const handleDelete = (user: User) => {
    setUserToDelete(user)
    setShowDeleteDialog(true)
  }

  // 确认删除用户
  const confirmDelete = async () => {
    if (!userToDelete) return

    try {
      await userService.delete(userToDelete.id)
      await fetchUsers()
      success('删除成功', `用户 "${userToDelete.username}" 已删除`)
    } catch (err: any) {
      console.error('Delete user error:', err)
      // 显示具体的错误信息
      const errorMessage = err?.message || err?.error || '删除用户失败，请重试'
      error('删除失败', errorMessage)
    } finally {
      setUserToDelete(null)
      setShowDeleteDialog(false)
    }
  }

  // 编辑用户
  const handleEdit = (user: User) => {
    setEditingUser(user)
    setShowForm(true)
  }

  // 表单提交成功
  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingUser(null)
    fetchUsers(users.page)
  }

  // 过滤用户
  const filteredUsers = users.data.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.nickname && user.nickname.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  if (!hasPermission('users:read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-500">您没有权限访问此页面</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">用户管理</h1>
            <p className="mt-1 text-sm text-gray-600">
              管理系统用户账户和权限
            </p>
          </div>
          {hasPermission('users:write') && (
            <div className="mt-4 sm:mt-0">
              <button
                onClick={() => setShowForm(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加用户
              </button>
            </div>
          )}
        </div>

        {/* 搜索框 */}
        <div className="max-w-md">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="搜索用户..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* 用户列表 */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">加载中...</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-gray-500">暂无用户数据</p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <li key={user.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                            <span className="text-white font-medium">
                              {(user.nickname || user.username).charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.nickname || user.username}
                            {user.nickname && (
                              <span className="ml-2 text-xs text-gray-500">(@{user.username})</span>
                            )}
                          </div>
                          {user.email && (
                            <div className="text-sm text-gray-500">
                              {user.email}
                            </div>
                          )}
                          <div className="text-sm text-gray-500">
                            角色: {user.role?.name} | 创建时间: {formatDate(user.created_at)}
                          </div>
                          <div className="flex gap-4 mt-1 text-xs text-gray-500">
                            <div title={formatFullTime((user as any).last_login_at)}>
                              最后登录: <span className="text-blue-600">{formatRelativeTime((user as any).last_login_at, '从未登录')}</span>
                            </div>
                            <div title={formatFullTime((user as any).last_active_at)}>
                              最近使用: <span className="text-green-600">{formatRelativeTime((user as any).last_active_at, '从未使用')}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {hasPermission('users:write') && (
                        <button
                          onClick={() => handleEdit(user)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      )}
                      {hasPermission('users:delete') && user.username !== 'admin' && (
                        <button
                          onClick={() => handleDelete(user)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* 分页 */}
        {users.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              显示 {(users.page - 1) * users.limit + 1} 到{' '}
              {Math.min(users.page * users.limit, users.total)} 条，共 {users.total} 条
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => fetchUsers(users.page - 1)}
                disabled={users.page <= 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>
              <button
                onClick={() => fetchUsers(users.page + 1)}
                disabled={users.page >= users.totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 用户表单弹窗 */}
      {showForm && (
        <UserForm
          user={editingUser}
          roles={roles}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowForm(false)
            setEditingUser(null)
          }}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setUserToDelete(null)
        }}
        onConfirm={confirmDelete}
        title="删除用户"
        message={`确定要删除用户 "${userToDelete?.username}" 吗？此操作无法撤销。`}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />
    </AdminLayout>
  )
}
