'use client'

import { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/layout/admin-layout'
import { useAuth } from '@/lib/auth-context'
import { Augment, User, PaginatedResponse, AugmentStats } from '@/lib/types'
import { Plus, Edit, Trash2, Search, Cloud, ExternalLink, Copy, ToggleLeft, ToggleRight } from 'lucide-react'
import { AugmentForm } from '@/components/augments/augment-form'
import { formatDate, calculateRemainingTime, formatRemainingTime } from '@/lib/utils'
import { augmentService, userService } from '@/lib/database'
import { useToast } from '@/components/ui/toast'
import { ConfirmDialog } from '@/components/ui/confirm-dialog'

export default function AugmentsPage() {
  const { hasPermission, user } = useAuth()
  const { success, error } = useToast()
  const [augments, setAugments] = useState<PaginatedResponse<Augment>>({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  })
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingAugment, setEditingAugment] = useState<Augment | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUserId, setSelectedUserId] = useState('')
  const [expireFilter, setExpireFilter] = useState<'all' | 'valid' | 'expired'>('all')
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [augmentToDelete, setAugmentToDelete] = useState<Augment | null>(null)
  const [stats, setStats] = useState<AugmentStats>({
    total: 0,
    used_count: 0,
    unused_count: 0,
    used_revenue: 0,
    total_revenue: 0
  })
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [showBatchDeleteDialog, setShowBatchDeleteDialog] = useState(false)
  const [showBatchAssignDialog, setShowBatchAssignDialog] = useState(false)
  const [batchAssignUserId, setBatchAssignUserId] = useState('')
  const [batchAssignPrice, setBatchAssignPrice] = useState('')

  // 检查是否是超级管理员
  const isSuperAdmin = user?.role?.name === '超级管理员'

  // 获取Authorization header
  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token')
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    }
  }

  // 获取Augment列表
  const fetchAugments = async (page = 1) => {
    try {
      const response = await fetch(`https://api-admin.liubao.site/api/augments?page=${page}&limit=10`, {
        headers: getAuthHeaders()
      })
      const result = await response.json()
      setAugments(result)
    } catch (error) {
      console.error('Failed to fetch augments:', error)
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const response = await fetch('https://api-admin.liubao.site/api/augments/stats', {
        headers: getAuthHeaders()
      })
      const result = await response.json()
      setStats(result)
    } catch (error) {
      console.error('Failed to fetch stats:', error)
    }
  }

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const result = await userService.getAll(1, 100)
      setUsers(result.data)
    } catch (error) {
      console.error('Failed to fetch users:', error)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchAugments(), fetchUsers(), fetchStats()])
      setLoading(false)
    }
    loadData()
  }, [])

  // 删除Augment
  const handleDelete = (augment: Augment) => {
    setAugmentToDelete(augment)
    setShowDeleteDialog(true)
  }

  // 确认删除Augment
  const confirmDelete = async () => {
    if (!augmentToDelete) return

    try {
      const response = await fetch(`https://api-admin.liubao.site/api/augments/${augmentToDelete.id}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      })

      if (response.ok) {
        await Promise.all([fetchAugments(augments.page), fetchStats()])
        success('删除成功', `Augment配置已删除`)
      } else {
        const result = await response.json()
        error('删除失败', result.error || '删除失败')
      }
    } catch (err) {
      console.error('Delete augment error:', err)
      error('删除失败', '网络错误，请重试')
    } finally {
      setAugmentToDelete(null)
      setShowDeleteDialog(false)
    }
  }

  // 切换状态
  const toggleStatus = async (augment: Augment) => {
    try {
      const response = await fetch(`https://api-admin.liubao.site/api/augments/${augment.id}/toggle-status`, {
        method: 'PATCH',
        headers: getAuthHeaders()
      })

      if (response.ok) {
        await Promise.all([fetchAugments(augments.page), fetchStats()])
        const newStatus = augment.status === 'used' ? '未使用' : '已使用'
        success('状态更新成功', `已切换为${newStatus}`)
      } else {
        const result = await response.json()
        error('状态更新失败', result.error || '更新失败')
      }
    } catch (err) {
      console.error('Toggle status error:', err)
      error('状态更新失败', '网络错误，请重试')
    }
  }

  // 编辑Augment
  const handleEdit = (augment: Augment) => {
    setEditingAugment(augment)
    setShowForm(true)
  }

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedIds.length === 0) {
      error('请选择要删除的项目', '')
      return
    }
    setShowBatchDeleteDialog(true)
  }

  // 确认批量删除
  const confirmBatchDelete = async () => {
    try {
      const response = await fetch('https://api-admin.liubao.site/api/augments/batch', {
        method: 'DELETE',
        headers: getAuthHeaders(),
        body: JSON.stringify({ ids: selectedIds })
      })

      if (response.ok) {
        const result = await response.json()
        await Promise.all([fetchAugments(augments.page), fetchStats()])
        setSelectedIds([])
        success('批量删除成功', `已删除 ${result.deletedCount} 个Augment`)
      } else {
        const result = await response.json()
        error('批量删除失败', result.error || '删除失败')
      }
    } catch (err) {
      console.error('Batch delete error:', err)
      error('批量删除失败', '网络错误，请重试')
    } finally {
      setShowBatchDeleteDialog(false)
    }
  }

  // 批量分配用户
  const handleBatchAssign = () => {
    if (selectedIds.length === 0) {
      error('请选择要分配的项目', '')
      return
    }
    setBatchAssignUserId('')
    setBatchAssignPrice('')
    setShowBatchAssignDialog(true)
  }

  // 确认批量分配用户
  const confirmBatchAssign = async () => {
    if (!batchAssignUserId) {
      error('请选择要分配的用户', '')
      return
    }

    try {
      const requestBody: any = { ids: selectedIds, userId: batchAssignUserId }

      // 如果提供了价格,则添加到请求体中
      if (batchAssignPrice && batchAssignPrice.trim() !== '') {
        const price = parseFloat(batchAssignPrice)
        if (isNaN(price) || price < 0) {
          error('请输入有效的价格', '')
          return
        }
        requestBody.price = price
      }

      const response = await fetch('https://api-admin.liubao.site/api/augments/batch-assign', {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(requestBody)
      })

      if (response.ok) {
        const result = await response.json()
        await Promise.all([fetchAugments(augments.page), fetchStats()])
        setSelectedIds([])
        setBatchAssignUserId('')
        setBatchAssignPrice('')
        success('批量分配成功', `已分配 ${result.assignedCount} 个Augment`)
      } else {
        const result = await response.json()
        error('批量分配失败', result.error || '分配失败')
      }
    } catch (err) {
      console.error('Batch assign error:', err)
      error('批量分配失败', '网络错误，请重试')
    } finally {
      setShowBatchAssignDialog(false)
    }
  }

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(filteredAugments.map(a => a.id))
    } else {
      setSelectedIds([])
    }
  }

  // 单选
  const handleSelectOne = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds([...selectedIds, id])
    } else {
      setSelectedIds(selectedIds.filter(selectedId => selectedId !== id))
    }
  }

  // 表单提交成功
  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingAugment(null)
    Promise.all([fetchAugments(augments.page), fetchStats()])
  }

  // 过滤Augment
  const filteredAugments = augments.data.filter(augment => {
    // 文本搜索过滤
    const matchesSearch = !searchTerm ||
      augment.tenant_url.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (augment.email && augment.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (augment.user_name && augment.user_name.toLowerCase().includes(searchTerm.toLowerCase()))

    // 用户过滤
    const matchesUser = !selectedUserId || augment.user_id === selectedUserId

    // 过期状态过滤
    const remainingTime = calculateRemainingTime(augment.created_at)
    const matchesExpire = expireFilter === 'all' ||
      (expireFilter === 'valid' && !remainingTime.isExpired) ||
      (expireFilter === 'expired' && remainingTime.isExpired)

    return matchesSearch && matchesUser && matchesExpire
  })

  // 计算过滤后的统计数据
  const filteredStats = {
    total: filteredAugments.length,
    used_count: filteredAugments.filter(a => a.status === 'used').length,
    unused_count: filteredAugments.filter(a => a.status === 'unused').length,
    valid_count: filteredAugments.filter(a => !calculateRemainingTime(a.created_at).isExpired).length,
    used_revenue: filteredAugments.filter(a => a.status === 'used').reduce((sum, a) => sum + (a.price || 0), 0),
    total_revenue: filteredAugments.reduce((sum, a) => sum + (a.price || 0), 0)
  }

  // 隐藏访问令牌
  const maskToken = (token: string) => {
    if (token.length <= 8) return token
    return token.substring(0, 4) + '****' + token.substring(token.length - 4)
  }

  // 复制Augment信息到剪贴板
  const handleCopy = async (augment: Augment) => {
    const copyText = `https://d6.api.augmentcode.com/

${augment.access_token}

${augment.portal_url || ''}

${augment.email || ''}

${augment.auth_session || ''}`

    try {
      await navigator.clipboard.writeText(copyText)
      success('复制成功', 'Augment信息已复制到剪贴板')
    } catch (err) {
      console.error('Failed to copy:', err)
      error('复制失败', '无法复制到剪贴板')
    }
  }

  if (!hasPermission('augments:read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-500">您没有权限访问此页面</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Augment管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理Augment连接配置和设置
          </p>
        </div>

        {/* 第一行:统计信息 */}
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="bg-blue-50 px-3 py-2 rounded-lg">
            <span className="text-blue-600 font-medium">总计: {filteredStats.total}</span>
          </div>
          <div className="bg-green-50 px-3 py-2 rounded-lg">
            <span className="text-green-600 font-medium">已使用: {filteredStats.used_count}</span>
          </div>
          <div className="bg-gray-50 px-3 py-2 rounded-lg">
            <span className="text-gray-600 font-medium">未使用: {filteredStats.unused_count}</span>
          </div>
          <div className="bg-purple-50 px-3 py-2 rounded-lg">
            <span className="text-purple-600 font-medium">未过期: {filteredStats.valid_count}</span>
          </div>
          <div className="bg-red-50 px-3 py-2 rounded-lg">
            <span className="text-red-600 font-medium">已使用总价值: ¥{filteredStats.used_revenue.toFixed(2)}</span>
          </div>
        </div>

        {/* 第二行:搜索框、筛选下拉框和添加按钮 */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 文本搜索框 */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="搜索Augment（支持租户URL、邮箱、用户名）..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* 用户过滤下拉框 */}
          <div className="min-w-[200px]">
            <select
              value={selectedUserId}
              onChange={(e) => setSelectedUserId(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">所有用户</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.nickname || user.username} {user.email && `(${user.email})`}
                </option>
              ))}
            </select>
          </div>

          {/* 过期状态过滤下拉框 */}
          <div className="min-w-[150px]">
            <select
              value={expireFilter}
              onChange={(e) => setExpireFilter(e.target.value as 'all' | 'valid' | 'expired')}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部状态</option>
              <option value="valid">未过期</option>
              <option value="expired">已过期</option>
            </select>
          </div>

          {/* 添加按钮 */}
          {hasPermission('augments:write') && (
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 whitespace-nowrap"
            >
              <Plus className="h-4 w-4 mr-2" />
              添加
            </button>
          )}
        </div>

        {/* 第三行:批量操作按钮 */}
        {isSuperAdmin && selectedIds.length > 0 && (
          <div className="flex gap-2">
            <button
              onClick={handleBatchAssign}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <Edit className="h-4 w-4 mr-2" />
              批量分配用户 ({selectedIds.length})
            </button>
            <button
              onClick={handleBatchDelete}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              批量删除 ({selectedIds.length})
            </button>
          </div>
        )}

        {/* 继续原有的统计信息显示 */}
        <div className="hidden">
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="bg-blue-50 px-3 py-2 rounded-lg">
              <span className="text-blue-600 font-medium">总计: {filteredStats.total}</span>
            </div>
            <div className="bg-green-50 px-3 py-2 rounded-lg">
              <span className="text-green-600 font-medium">已使用: {filteredStats.used_count}</span>
            </div>
            <div className="bg-gray-50 px-3 py-2 rounded-lg">
              <span className="text-gray-600 font-medium">未使用: {filteredStats.unused_count}</span>
            </div>
            <div className="bg-purple-50 px-3 py-2 rounded-lg">
              <span className="text-purple-600 font-medium">未过期: {filteredStats.valid_count}</span>
            </div>
            <div className="bg-red-50 px-3 py-2 rounded-lg">
              <span className="text-red-600 font-medium">已使用总价值: ¥{filteredStats.used_revenue.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Augment表格 */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">加载中...</p>
            </div>
          ) : filteredAugments.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-gray-500">暂无Augment数据</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {isSuperAdmin && (
                      <th className="px-6 py-3 text-left">
                        <input
                          type="checkbox"
                          checked={selectedIds.length === filteredAugments.length && filteredAugments.length > 0}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </th>
                    )}
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tenant URL
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      访问令牌
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Portal URL
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      邮箱
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Auth Session
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      所属用户
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      价格
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      剩余时间
                    </th>
                    <th className="sticky right-0 bg-gray-50 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAugments.map((augment) => (
                    <tr key={augment.id} className="hover:bg-gray-50">
                      {isSuperAdmin && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedIds.includes(augment.id)}
                            onChange={(e) => handleSelectOne(augment.id, e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center">
                              <Cloud className="h-4 w-4 text-white" />
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="flex items-center">
                              <div className="text-sm font-medium text-gray-900">
                                {augment.tenant_url}
                              </div>
                              <a
                                href={augment.tenant_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-2 text-gray-400 hover:text-gray-600"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-mono">
                          {maskToken(augment.access_token)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {augment.portal_url || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {augment.email || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate" title={augment.auth_session || '-'}>
                          {augment.auth_session ? (
                            <span className="font-mono text-xs">
                              {augment.auth_session.length > 30
                                ? `${augment.auth_session.substring(0, 30)}...`
                                : augment.auth_session}
                            </span>
                          ) : '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {augment.user_nickname || augment.user_name || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <button
                            onClick={() => toggleStatus(augment)}
                            className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              augment.status === 'used'
                                ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                            }`}
                          >
                            {augment.status === 'used' ? (
                              <>
                                <ToggleRight className="h-3 w-3 mr-1" />
                                已使用
                              </>
                            ) : (
                              <>
                                <ToggleLeft className="h-3 w-3 mr-1" />
                                未使用
                              </>
                            )}
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-medium">
                          ¥{augment.price?.toFixed(2) || '0.00'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDate(augment.created_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {(() => {
                          const remainingTime = calculateRemainingTime(augment.created_at)
                          const formattedTime = formatRemainingTime(remainingTime)
                          return (
                            <div className={`text-sm font-medium ${remainingTime.isExpired ? 'text-red-600' : 'text-green-600'}`}>
                              {formattedTime}
                            </div>
                          )
                        })()}
                      </td>
                      <td className="sticky right-0 bg-white px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleCopy(augment)}
                            className="text-green-600 hover:text-green-900"
                            title="复制配置信息"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                          {hasPermission('augments:write') && (
                            <button
                              onClick={() => handleEdit(augment)}
                              className="text-blue-600 hover:text-blue-900"
                              title="编辑"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                          )}
                          {hasPermission('augments:delete') && (
                            <button
                              onClick={() => handleDelete(augment)}
                              className="text-red-600 hover:text-red-900"
                              title="删除"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 分页 */}
        {augments.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              显示 {(augments.page - 1) * augments.limit + 1} 到{' '}
              {Math.min(augments.page * augments.limit, augments.total)} 条，共 {augments.total} 条
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => fetchAugments(augments.page - 1)}
                disabled={augments.page <= 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>
              <button
                onClick={() => fetchAugments(augments.page + 1)}
                disabled={augments.page >= augments.totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Augment表单弹窗 */}
      {showForm && (
        <AugmentForm
          augment={editingAugment}
          users={users}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowForm(false)
            setEditingAugment(null)
          }}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setAugmentToDelete(null)
        }}
        onConfirm={confirmDelete}
        title="删除Augment配置"
        message={`确定要删除此Augment配置吗？此操作无法撤销。`}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />

      {/* 批量删除确认对话框 */}
      <ConfirmDialog
        isOpen={showBatchDeleteDialog}
        onClose={() => setShowBatchDeleteDialog(false)}
        onConfirm={confirmBatchDelete}
        title="批量删除Augment配置"
        message={`确定要删除选中的 ${selectedIds.length} 个Augment配置吗？此操作无法撤销。`}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />

      {/* 批量分配用户对话框 */}
      {showBatchAssignDialog && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                批量分配用户
              </h3>
              <div className="mt-2">
                <p className="text-sm text-gray-500 mb-4">
                  将选中的 {selectedIds.length} 个Augment分配给指定用户
                </p>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择用户
                  </label>
                  <select
                    value={batchAssignUserId}
                    onChange={(e) => setBatchAssignUserId(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    <option value="">请选择用户</option>
                    {users.map((user) => (
                      <option key={user.id} value={user.id}>
                        {user.nickname || user.username} ({user.email})
                      </option>
                    ))}
                  </select>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    设置价格 (可选)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={batchAssignPrice}
                    onChange={(e) => setBatchAssignPrice(e.target.value)}
                    placeholder="不填则保持原价格"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    如果不填写价格,则保持Augment原有价格不变
                  </p>
                </div>
              </div>
              <div className="flex gap-2 justify-end">
                <button
                  onClick={() => setShowBatchAssignDialog(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  取消
                </button>
                <button
                  onClick={confirmBatchAssign}
                  className="px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  确认分配
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
