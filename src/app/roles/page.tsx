'use client'

import { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/layout/admin-layout'
import { useAuth } from '@/lib/auth-context'
import { Role } from '@/lib/types'
import { Plus, Edit, Trash2, Shield } from 'lucide-react'
import { RoleForm } from '@/components/roles/role-form'
import { formatDate } from '@/lib/utils'
import { roleService } from '@/lib/database'

export default function RolesPage() {
  const { hasPermission } = useAuth()
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const result = await roleService.getAll()
      setRoles(result.data)
    } catch (error) {
      console.error('Failed to fetch roles:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRoles()
  }, [])

  // 删除角色
  const handleDelete = async (role: Role) => {
    if (!confirm(`确定要删除角色 "${role.name}" 吗？`)) {
      return
    }

    try {
      await roleService.delete(role.id)
      await fetchRoles()
      alert('角色删除成功')
    } catch (error) {
      console.error('Delete role error:', error)
      alert('演示模式不支持删除角色')
    }
  }

  // 编辑角色
  const handleEdit = (role: Role) => {
    setEditingRole(role)
    setShowForm(true)
  }

  // 表单提交成功
  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingRole(null)
    fetchRoles()
  }

  // 获取权限显示名称
  const getPermissionDisplayName = (permission: string) => {
    const permissionNames: Record<string, string> = {
      'admin': '超级管理员',
      'users:read': '查看用户',
      'users:write': '编辑用户',
      'users:delete': '删除用户',
      'roles:read': '查看角色',
      'roles:write': '编辑角色',
      'roles:delete': '删除角色',
      'augments:read': '查看Augment',
      'augments:write': '编辑Augment',
      'augments:delete': '删除Augment',
    }
    return permissionNames[permission] || permission
  }

  if (!hasPermission('roles:read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-500">您没有权限访问此页面</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">角色管理</h1>
            <p className="mt-1 text-sm text-gray-600">
              管理系统角色和权限配置
            </p>
          </div>
          {hasPermission('roles:write') && (
            <div className="mt-4 sm:mt-0">
              <button
                onClick={() => setShowForm(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加角色
              </button>
            </div>
          )}
        </div>

        {/* 角色列表 */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">加载中...</p>
            </div>
          ) : roles.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-gray-500">暂无角色数据</p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {roles.map((role) => (
                <li key={role.id} className="px-6 py-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-green-600 flex items-center justify-center">
                            <Shield className="h-5 w-5 text-white" />
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            {role.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            创建时间: {formatDate(role.created_at)}
                          </div>
                          <div className="mt-2">
                            <div className="text-sm text-gray-700 mb-1">权限:</div>
                            <div className="flex flex-wrap gap-1">
                              {role.permissions.map((permission) => (
                                <span
                                  key={permission}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                >
                                  {getPermissionDisplayName(permission)}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {hasPermission('roles:write') && role.name !== '超级管理员' && (
                        <button
                          onClick={() => handleEdit(role)}
                          className="text-blue-600 hover:text-blue-900"
                          title="编辑角色"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      )}
                      {hasPermission('roles:delete') && 
                       !['超级管理员', '管理员', '用户'].includes(role.name) && (
                        <button
                          onClick={() => handleDelete(role)}
                          className="text-red-600 hover:text-red-900"
                          title="删除角色"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* 权限说明 */}
        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              权限说明
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">用户管理</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>• 查看用户：查看用户列表</div>
                  <div>• 编辑用户：创建和修改用户</div>
                  <div>• 删除用户：删除用户账户</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">角色管理</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>• 查看角色：查看角色列表</div>
                  <div>• 编辑角色：创建和修改角色</div>
                  <div>• 删除角色：删除自定义角色</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Augment管理</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>• 查看Augment：查看Augment列表</div>
                  <div>• 编辑Augment：创建和修改Augment</div>
                  <div>• 删除Augment：删除Augment配置</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 角色表单弹窗 */}
      {showForm && (
        <RoleForm
          role={editingRole}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowForm(false)
            setEditingRole(null)
          }}
        />
      )}
    </AdminLayout>
  )
}
