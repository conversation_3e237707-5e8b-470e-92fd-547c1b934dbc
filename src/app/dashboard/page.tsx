'use client'

import { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/layout/admin-layout'
import { useAuth } from '@/lib/auth-context'
import { Users, Shield, Cloud, Activity, DollarSign } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DashboardStats {
  totalUsers: number
  totalRoles: number
  totalAugments: number
  recentActivity: number
  usedRevenue: number
  totalRevenue: number
}

export default function DashboardPage() {
  const { user, hasPermission } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalRoles: 0,
    totalAugments: 0,
    recentActivity: 0,
    usedRevenue: 0,
    totalRevenue: 0
  })
  const [loading, setLoading] = useState(true)

  // 获取Authorization header
  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token')
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    }
  }

  useEffect(() => {
    // 获取真实的统计数据
    const fetchStats = async () => {
      try {
        setLoading(true)

        // 并行获取所有统计数据
        const [usersRes, rolesRes, augmentsRes] = await Promise.all([
          fetch('https://api-admin.liubao.site/api/users/stats', {
            headers: getAuthHeaders()
          }),
          fetch('https://api-admin.liubao.site/api/roles/stats', {
            headers: getAuthHeaders()
          }),
          fetch('https://api-admin.liubao.site/api/augments/stats', {
            headers: getAuthHeaders()
          })
        ])

        const [usersData, rolesData, augmentsData] = await Promise.all([
          usersRes.json(),
          rolesRes.json(),
          augmentsRes.json()
        ])

        setStats({
          totalUsers: usersData.total || 0,
          totalRoles: rolesData.total || 0,
          totalAugments: augmentsData.total || 0,
          recentActivity: augmentsData.used_count || 0, // 使用已使用的Augment数量作为活动指标
          usedRevenue: augmentsData.used_revenue || 0,
          totalRevenue: augmentsData.total_revenue || 0
        })
      } catch (error) {
        console.error('Failed to fetch stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statsCards = [
    {
      name: '用户总数',
      value: stats.totalUsers,
      icon: Users,
      color: 'bg-blue-500',
      permission: 'users:read'
    },
    {
      name: '角色总数',
      value: stats.totalRoles,
      icon: Shield,
      color: 'bg-green-500',
      permission: 'roles:read'
    },
    {
      name: 'Augment总数',
      value: stats.totalAugments,
      icon: Cloud,
      color: 'bg-purple-500',
      permission: 'augments:read'
    },
    {
      name: '已使用Augment',
      value: stats.recentActivity,
      icon: Activity,
      color: 'bg-orange-500',
      permission: null
    },
    {
      name: '已使用总价值',
      value: `¥${stats.usedRevenue.toFixed(2)}`,
      icon: DollarSign,
      color: 'bg-red-500',
      permission: 'augments:read'
    },
    {
      name: '全部总价值',
      value: `¥${stats.totalRevenue.toFixed(2)}`,
      icon: DollarSign,
      color: 'bg-yellow-500',
      permission: 'augments:read'
    }
  ]

  // 过滤用户有权限查看的统计卡片
  const filteredStats = statsCards.filter(card => 
    !card.permission || hasPermission(card.permission)
  )

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">仪表板</h1>
          <p className="mt-1 text-sm text-gray-600">
            欢迎回来，{user?.username}！这里是系统概览。
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {filteredStats.map((item) => (
            <div
              key={item.name}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className={cn("absolute rounded-md p-3", item.color)}>
                  <item.icon className="h-6 w-6 text-white" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 truncate">
                  {item.name}
                </p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">
                  {loading ? (
                    <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                  ) : (
                    item.value
                  )}
                </p>
              </dd>
            </div>
          ))}
        </div>

        {/* 快速操作 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              快速操作
            </h3>
            <div className="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {hasPermission('users:write') && (
                <a
                  href="/users"
                  className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-gray-400"
                >
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                      <Users className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      管理用户
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      添加、编辑或删除系统用户
                    </p>
                  </div>
                </a>
              )}

              {hasPermission('roles:write') && (
                <a
                  href="/roles"
                  className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-gray-400"
                >
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                      <Shield className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      管理角色
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      配置角色权限和分配
                    </p>
                  </div>
                </a>
              )}

              {hasPermission('augments:write') && (
                <a
                  href="/augments"
                  className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-gray-400"
                >
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                      <Cloud className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      管理Augment
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      配置Augment连接和设置
                    </p>
                  </div>
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
